// ABOUTME: This file defines a reusable Accordion component.
// ABOUTME: It's used in MDX to create collapsible content sections.

'use client';

import { useState, type ReactNode } from 'react';

interface AccordionProps {
  title: string;
  children: ReactNode;
}

export default function Accordion({ title, children }: AccordionProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border-l-2 border-gray-700 pl-3 my-2">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center w-full text-left text-cyan-400 hover:text-cyan-300 transition-colors"
      >
        <span className={`transform transition-transform duration-200 ${isOpen ? 'rotate-90' : 'rotate-0'}`}>
          ▶
        </span>
        <span className="ml-2 font-bold">{title}</span>
      </button>
      {isOpen && (
        <div className="mt-2 text-gray-300">
          {children}
        </div>
      )}
    </div>
  );
}