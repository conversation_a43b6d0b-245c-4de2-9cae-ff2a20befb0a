// ABOUTME: This file defines a reusable Accordion component.
// ABOUTME: It's used in MDX to create collapsible content sections.

'use client';

import { useState, type ReactNode } from 'react';

interface AccordionProps {
  title: string;
  children: ReactNode;
}

export default function Accordion({ title, children }: AccordionProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="my-4">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center w-full text-left text-white hover:text-gray-300 transition-colors duration-150 py-2 px-3 -mx-3 rounded hover:bg-gray-900"
      >
        <span className={`transform transition-transform duration-200 text-gray-400 ${isOpen ? 'rotate-90' : 'rotate-0'}`}>
          →
        </span>
        <span className="ml-3 font-medium">{title}</span>
      </button>
      {isOpen && (
        <div className="mt-3 ml-6 text-gray-300 leading-relaxed">
          {children}
        </div>
      )}
    </div>
  );
}