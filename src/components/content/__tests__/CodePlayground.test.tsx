import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import CodePlayground from '../CodePlayground';

// Mock timers for testing async behavior
jest.useFakeTimers();

describe('CodePlayground', () => {
  afterEach(() => {
    jest.clearAllTimers();
  });

  it('should render with title and initial code', () => {
    render(
      <CodePlayground 
        title="Test Playground"
        initialCode="console.log('hello');"
        description="Test description"
      />
    );

    expect(screen.getByText('Test Playground')).toBeInTheDocument();
    expect(screen.getByText('Test description')).toBeInTheDocument();
    expect(screen.getByDisplayValue("console.log('hello');")).toBeInTheDocument();
  });

  it('should allow code editing', () => {
    render(
      <CodePlayground 
        title="Test Playground"
        initialCode="console.log('hello');"
      />
    );

    const textarea = screen.getByRole('textbox');
    fireEvent.change(textarea, { target: { value: 'new code' } });
    
    expect(screen.getByDisplayValue('new code')).toBeInTheDocument();
  });

  it('should show running state when run button is clicked', async () => {
    render(
      <CodePlayground 
        title="Test Playground"
        initialCode="const agent = new Agent();"
      />
    );

    const runButton = screen.getByText('Run');
    fireEvent.click(runButton);

    expect(screen.getByText('Running...')).toBeInTheDocument();
    expect(screen.getByText('Executing...')).toBeInTheDocument();
    
    // Fast forward timers to complete the execution
    jest.runAllTimers();
    
    await waitFor(() => {
      expect(screen.getByText('Run')).toBeInTheDocument();
      expect(screen.getByText(/Agent initialized successfully/)).toBeInTheDocument();
    });
  });
});
