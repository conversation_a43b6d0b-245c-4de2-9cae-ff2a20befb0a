import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import Accordion from '../Accordion';

describe('Accordion', () => {
  it('should render with title and be closed by default', () => {
    render(
      <Accordion title="Test Accordion">
        <p>Test content</p>
      </Accordion>
    );

    expect(screen.getByText('Test Accordion')).toBeInTheDocument();
    expect(screen.queryByText('Test content')).not.toBeInTheDocument();
  });

  it('should open and show content when clicked', () => {
    render(
      <Accordion title="Test Accordion">
        <p>Test content</p>
      </Accordion>
    );

    const button = screen.getByRole('button');
    fireEvent.click(button);

    expect(screen.getByText('Test content')).toBeInTheDocument();
  });

  it('should close when clicked again', () => {
    render(
      <Accordion title="Test Accordion">
        <p>Test content</p>
      </Accordion>
    );

    const button = screen.getByRole('button');
    
    // Open
    fireEvent.click(button);
    expect(screen.getByText('Test content')).toBeInTheDocument();
    
    // Close
    fireEvent.click(button);
    expect(screen.queryByText('Test content')).not.toBeInTheDocument();
  });
});
