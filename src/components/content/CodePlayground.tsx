'use client';

import { useState } from 'react';

interface CodePlaygroundProps {
  title: string;
  initialCode: string;
  description?: string;
}

export default function CodePlayground({ title, initialCode, description }: CodePlaygroundProps) {
  const [code, setCode] = useState(initialCode);
  const [output, setOutput] = useState('');
  const [isRunning, setIsRunning] = useState(false);

  const runCode = async () => {
    setIsRunning(true);
    setOutput('');
    
    // Simulate code execution with realistic delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock output based on code content
    if (code.includes('Agent')) {
      setOutput(`✓ Agent initialized successfully
✓ Capabilities loaded: ${code.match(/capabilities: \[(.*?)\]/)?.[1] || 'default'}
✓ Task function registered
→ Agent ready for execution`);
    } else if (code.includes('pipeline')) {
      setOutput(`✓ Pipeline created with ${code.split('new Agent').length - 1} agents
✓ Orchestration layer initialized
✓ Inter-agent communication established
→ Pipeline ready for execution`);
    } else {
      setOutput('✓ Code executed successfully\n→ Ready for next operation');
    }
    
    setIsRunning(false);
  };

  return (
    <div className="my-6 border border-gray-800 rounded-lg overflow-hidden bg-gray-950">
      <div className="px-4 py-3 bg-gray-900 border-b border-gray-800">
        <h3 className="text-white font-medium">{title}</h3>
        {description && <p className="text-gray-400 text-sm mt-1">{description}</p>}
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
        {/* Code Editor */}
        <div className="relative">
          <div className="px-3 py-2 bg-gray-800 text-gray-400 text-xs font-mono border-b border-gray-700">
            agent.ts
          </div>
          <textarea
            value={code}
            onChange={(e) => setCode(e.target.value)}
            className="w-full h-64 p-4 bg-gray-950 text-gray-100 font-mono text-sm resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
            spellCheck={false}
          />
        </div>
        
        {/* Output */}
        <div className="border-l border-gray-800">
          <div className="px-3 py-2 bg-gray-800 text-gray-400 text-xs font-mono border-b border-gray-700 flex justify-between items-center">
            <span>output</span>
            <button
              onClick={runCode}
              disabled={isRunning}
              className="px-3 py-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white text-xs rounded transition-colors"
            >
              {isRunning ? 'Running...' : 'Run'}
            </button>
          </div>
          <div className="h-64 p-4 bg-black text-green-400 font-mono text-sm overflow-y-auto">
            {isRunning ? (
              <div className="flex items-center">
                <div className="animate-spin w-4 h-4 border-2 border-green-400 border-t-transparent rounded-full mr-2"></div>
                Executing...
              </div>
            ) : (
              <pre className="whitespace-pre-wrap">{output || 'Click "Run" to execute the code'}</pre>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
