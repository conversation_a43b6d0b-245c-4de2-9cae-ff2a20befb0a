// src/components/terminal/TypeWriter.tsx
'use client';
import React, { useState, useEffect } from 'react';

interface TypeWriterProps {
  text: string;
  delay?: number;
  onComplete?: () => void;
}

const TypeWriter: React.FC<TypeWriterProps> = ({ text, delay = 20, onComplete }) => {
  const [displayedText, setDisplayedText] = useState('');
  useEffect(() => {
    let i = 0;
    const type = () => {
      if (i < text.length) {
        setDisplayedText(text.substring(0, i + 1));
        i++;
        setTimeout(type, delay);
      } else if (onComplete) {
        onComplete();
      }
    };
    type();
  }, [text, delay, onComplete]);

  return <span>{displayedText}</span>;
};

export default TypeWriter;