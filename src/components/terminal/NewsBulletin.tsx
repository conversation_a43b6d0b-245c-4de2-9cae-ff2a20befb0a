// ABOUTME: This component renders a formatted "Latest Intel" box.
// ABOUTME: It animates line-by-line for a more engaging presentation.

'use client';

import { useState, useEffect } from 'react';
import { Typewriter } from './Typewriter';

interface NewsBulletinProps {
  onComplete: () => void;
}

const newsLines = [
  '┌─[ LATEST INTEL ]────────────────────────────────┐',
  '│                                                 │',
  '│  EspirAI Labs completes acquisition of Bokoo,   │',
  '│  unlocking end-to-end data capabilities.        │',
  '│                                                 │',
  "│  Type 'cat pages/bokoo' for more details.       │",
  '│                                                 │',
  '└─────────────────────────────────────────────────┘',
];

export default function NewsBulletin({ onComplete }: NewsBulletinProps) {
  const [visibleLineIndex, setVisibleLineIndex] = useState(-1);

  useEffect(() => {
    // Stagger the appearance of each line
    const timer = setTimeout(() => {
      setVisibleLineIndex(0);
    }, 200); // Initial delay before the box starts drawing

    return () => clearTimeout(timer);
  }, []);

  const handleLineComplete = () => {
    if (visibleLineIndex < newsLines.length - 1) {
      setVisibleLineIndex(prev => prev + 1);
    } else {
      onComplete(); // Fire the final callback when the last line is done
    }
  };

  if (visibleLineIndex === -1) {
    return null; // Don't render anything initially
  }

  return (
    <div>
      {newsLines.slice(0, visibleLineIndex + 1).map((line, index) => {
        // Only the currently appearing line should be animated
        if (index === visibleLineIndex) {
          return <Typewriter key={index} text={line} onComplete={handleLineComplete} speed={5} />;
        }
        // Previously completed lines are rendered instantly
        return <div key={index}>{line}</div>;
      })}
    </div>
  );
}