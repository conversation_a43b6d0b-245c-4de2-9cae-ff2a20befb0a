// ABOUTME: This is the main stateful component for the terminal experience.
// ABOUTME: It manages user input, command history, and renders MDX content.

"use client";

import { useState, useEffect, useRef } from 'react';
import { processCommand, CommandOutput, mdxComponents } from '@/lib/command-engine';
import { ConnectFormData } from '@/lib/types';
import { useMDXComponent } from 'next-contentlayer/hooks';

// --- HELPER COMPONENTS --- //

const Typewriter = ({ text, speed = 20, onComplete }: { text: string; speed?: number; onComplete?: () => void; }) => {
  const [displayedText, setDisplayedText] = useState('');
  useEffect(() => {
    setDisplayedText('');
    let i = 0;
    const intervalId = setInterval(() => {
      setDisplayedText(prev => prev + text.charAt(i));
      i++;
      if (i >= text.length) {
        clearInterval(intervalId);
        onComplete?.();
      }
    }, speed);
    return () => clearInterval(intervalId);
  }, [text, speed, onComplete]);
  return <>{displayedText}</>;
};

const MdxContent = ({ code, components }: { code: string, components: any }) => {
  const MDXComponent = useMDXComponent(code);
  return <MDXComponent components={components} />;
};

const ListOutput = ({ items, onCommand, onHover }: { items: any[], onCommand: (cmd: string) => void, onHover: (cmd: string | null) => void }) => {
  return (
    <div className="flex flex-col">
      {items.map(item => (
        <ListItem key={item.name} item={item} onCommand={onCommand} onHover={onHover} />
      ))}
    </div>
  );
};

const ListItem = ({ item, onCommand, onHover }: { item: any, onCommand: (cmd: string) => void, onHover: (cmd: string | null) => void }) => {
  const [hovered, setHovered] = useState(false);
  const getCommand = (item: any) => item.type === 'directory' ? 'services' : item.name;
  return (
    <div
      className="flex items-center cursor-pointer py-1 px-2 -mx-2 rounded transition-colors duration-150 hover:bg-gray-900"
      onClick={() => onCommand(getCommand(item))}
      onMouseEnter={() => { setHovered(true); onHover(getCommand(item)); }}
      onMouseLeave={() => { setHovered(false); onHover(null); }}
    >
      <span className={`w-4 text-gray-500 transition-opacity duration-200 ${hovered ? 'opacity-100' : 'opacity-0'}`}>
        →
      </span>
      <span className={`${item.type === 'directory' ? 'text-blue-400' : 'text-white'} ml-1`}>
        {item.name}
      </span>
      {item.type === 'directory' && <span className="text-gray-500 ml-1">/</span>}
    </div>
  );
};

const OutputLine = ({ data, onCommand, onHover }: { data: any; onCommand: any; onHover: any; }) => {
  // Strings are passed to Typewriter for the "alive" effect.
  if (typeof data === 'string') return <Typewriter text={data} />;
  // Arrays are passed to ListOutput for clean list styling.
  if (Array.isArray(data)) return <ListOutput items={data} onCommand={onCommand} onHover={onHover} />;
  // MDX content is rendered instantly for better UX with long-form text.
  if (data?.type === 'MDX_CONTENT') return <MdxContent code={data.code} components={data.components} />;
  // It can also render raw JSX elements (like the command history line).
  return data;
};

// --- MAIN COMPONENT --- //
export default function EspiraiOS() {
  const [output, setOutput] = useState<React.ReactNode[]>([]);
  const [isAnimating, setIsAnimating] = useState(true);
  const [typedInput, setTypedInput] = useState('');
  const [hoveredCommand, setHoveredCommand] = useState<string | null>(null);
  const [showPrompt, setShowPrompt] = useState(false);
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectStep, setConnectStep] = useState(0);
  const [connectAnswers, setConnectAnswers] = useState<Partial<ConnectFormData>>({});

  const endOfOutputRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const connectQuestions = [ { key: 'name', question: 'What name should we use?' }, { key: 'email', question: () => `And your email, ${connectAnswers.name}?` }, { key: 'challenge', question: 'Tell us about the challenge or opportunity on your mind.' }, { key: 'area', question: 'From what you\'ve described, does it fall into one of these areas? (automation, data, custom-ai, etc.)' }, { key: 'interest', question: 'A key part of our model is result-based pricing—we\'re incentivized by the performance of what we build. Is exploring an accountable partnership like this of interest? (yes/no)' }, ];
  const handleCommandSubmit = (commandToSubmit: string) => { if (isConnecting) { handleConnectSubmit(commandToSubmit); } else { handleTerminalCommand(commandToSubmit); } };
  const handleTerminalCommand = (command: string) => {
    if (command.trim() === '') return;
    setIsAnimating(false);
    setHoveredCommand(null);
    setCommandHistory(prev => [command, ...prev]);
    setHistoryIndex(-1);

    const commandHistoryLine = (
      <div className="flex text-gray-400">
        <span className="text-gray-500">→ </span>
        <span>{command}</span>
      </div>
    );

    const result = processCommand(command);
    if (result.__signal__ === 'CLEAR') {
      setOutput([]);
    } else if (result.__signal__ === 'ENGAGE_CONNECT') {
      setOutput(prev => [...prev, commandHistoryLine, `[+] Initializing secure connection to EspirAI Lab...`]);
      setIsConnecting(true);
      setConnectStep(0);
      setConnectAnswers({});
    } else {
      setOutput(prev => [...prev, commandHistoryLine, result.data]);
    }
    setTypedInput('');
  };
  const handleConnectSubmit = async (answer: string) => { if (!answer.trim()) return; setIsAnimating(false); const currentQuestion = connectQuestions[connectStep]; const questionText = typeof currentQuestion.question === 'function' ? currentQuestion.question() : currentQuestion.question; const questionLine = ( <p className="text-gray-300"> <span className="text-cyan-400 mr-2">{`[${connectStep + 1}/${connectQuestions.length}]`}</span> {questionText} </p> ); const answerLine = ( <p> <span className="text-green-400 mr-2">{'>'}</span> {answer} </p> ); setOutput(prev => [...prev, questionLine, answerLine]); const newAnswers = { ...connectAnswers, [currentQuestion.key]: answer }; setConnectAnswers(newAnswers); setTypedInput(''); if (connectStep < connectQuestions.length - 1) { setConnectStep(connectStep + 1); } else { setIsConnecting(false); setOutput(prev => [...prev, "Submitting your request..."]); try { const response = await fetch('/api/connect', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(newAnswers), }); if (!response.ok) throw new Error('API request failed'); const successMessage = `[+] Got it. We thrive on accountability. Your inquiry has been received. We'll review your notes and be in touch at ${newAnswers.email} within one business day.`; setOutput(prev => [...prev, successMessage]); } catch (error) { const errorMessage = `[!] Connection failed. Please try again later or contact us directly.`; setOutput(prev => [...prev, errorMessage]); } } };
  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => { if (isAnimating) { event.preventDefault(); return; } const currentInput = hoveredCommand || typedInput; if (event.key === 'Enter') { event.preventDefault(); handleCommandSubmit(currentInput); } else if (event.key === 'ArrowUp' && !isConnecting) { event.preventDefault(); const newIndex = Math.min(historyIndex + 1, commandHistory.length - 1); if (newIndex >= 0) { setHistoryIndex(newIndex); setTypedInput(commandHistory[newIndex]); setHoveredCommand(null); } } else if (event.key === 'ArrowDown' && !isConnecting) { event.preventDefault(); const newIndex = Math.max(historyIndex - 1, -1); setHistoryIndex(newIndex); setTypedInput(newIndex >= 0 ? commandHistory[newIndex] : ''); setHoveredCommand(null); } };

  // Clean, professional intro sequence inspired by sdsa.ai
  useEffect(() => {
    const welcomeLine1 = "We're building the next-gen operating system for AI agents.";
    const aboutLine = "Modern AI will fundamentally change how people use software in their daily lives.";
    const visionLine = "We're creating the infrastructure that makes it possible to build robust, agentic applications.";
    const newsLine = "EspirAI Labs completes acquisition of Bokoo → Type 'cat news/bokoo-acquisition' for details";
    const hintLine = "Type 'help' to see available commands";

    let isSequenceComplete = false;

    const handleLine1Complete = () => {
      if (isSequenceComplete) return;
      setOutput([
        welcomeLine1,
        <div key="spacer-1" className="mt-4" />,
        <Typewriter key="line-2" text={aboutLine} onComplete={handleLine2Complete} />
      ]);
    };

    const handleLine2Complete = () => {
      if (isSequenceComplete) return;
      setOutput([
        welcomeLine1,
        <div key="spacer-1" className="mt-4" />,
        aboutLine,
        <div key="spacer-2" className="mt-2" />,
        <Typewriter key="line-3" text={visionLine} onComplete={handleLine3Complete} />
      ]);
    };

    const handleLine3Complete = () => {
      if (isSequenceComplete) return;
      setOutput([
        welcomeLine1,
        <div key="spacer-1" className="mt-4" />,
        aboutLine,
        <div key="spacer-2" className="mt-2" />,
        visionLine,
        <div key="spacer-3" className="mt-4" />,
        <Typewriter key="line-4" text={newsLine} onComplete={handleNewsComplete} />
      ]);
    };

    const handleNewsComplete = () => {
      if (isSequenceComplete) return;
      isSequenceComplete = true;
      setOutput([
        welcomeLine1,
        <div key="spacer-1" className="mt-4" />,
        aboutLine,
        <div key="spacer-2" className="mt-2" />,
        visionLine,
        <div key="spacer-3" className="mt-4" />,
        newsLine,
        <div key="spacer-4" className="mt-6" />,
        <div key="hint" className="text-gray-400 text-sm">{hintLine}</div>,
        <div key="spacer-5" className="mt-4" />
      ]);
      setShowPrompt(true);
      setHoveredCommand('help'); // Auto-populate the 'help' command
      setIsAnimating(false); // Allow user input
    };

    // Start the sequence only once
    setOutput([<Typewriter key="line-1" text={welcomeLine1} onComplete={handleLine1Complete} />]);

    // Cleanup function to prevent memory leaks
    return () => {
      isSequenceComplete = true;
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => { endOfOutputRef.current?.scrollIntoView({ behavior: 'auto' }); }, [output]);
  useEffect(() => { if (!isAnimating) { inputRef.current?.focus(); } }, [isAnimating, isConnecting, showPrompt]);

  const renderPrompt = () => {
    if (isConnecting) {
      const currentQuestion = connectQuestions[connectStep];
      const questionText = typeof currentQuestion.question === 'function'
        ? currentQuestion.question()
        : currentQuestion.question;
      return (
        <label htmlFor="terminal-input" className="text-blue-400 whitespace-nowrap">
          {`[${connectStep + 1}/${connectQuestions.length}] ${questionText} >`}
        </label>
      );
    }
    return <span className="text-gray-400">→</span>;
  };
  const displayInput = hoveredCommand || typedInput;

  return (
    <div className="flex flex-col h-screen bg-black text-white" onClick={() => !isAnimating && inputRef.current?.focus()}>
      {/* Header with clean branding */}
      <div className="flex-shrink-0 px-6 py-4 border-b border-gray-800">
        <div className="max-w-4xl">
          <span className="text-gray-400">/dev/</span>
          <span className="text-white">espirai</span>
        </div>
      </div>

      {/* Main terminal content */}
      <div className="flex-1 overflow-y-auto flex flex-col justify-end px-6 py-4">
        <div className="whitespace-pre-wrap max-w-4xl space-y-2">
          {output.map((line, index) => (
            <div key={index} className="leading-relaxed">
              <OutputLine data={line} onCommand={handleTerminalCommand} onHover={setHoveredCommand} />
            </div>
          ))}
        </div>
        <div ref={endOfOutputRef} />
      </div>

      {/* Clean prompt area */}
      {showPrompt && (
        <div className="flex-shrink-0 px-6 py-4 border-t border-gray-800">
          <div className="flex items-center max-w-4xl">
            {renderPrompt()}
            <div className="flex-1 ml-2">{displayInput}</div>
            <input
              id="terminal-input"
              ref={inputRef}
              className="absolute opacity-0 pointer-events-none"
              value={typedInput}
              onChange={(e) => { setTypedInput(e.target.value); setHoveredCommand(null); }}
              onKeyDown={handleKeyDown}
              autoFocus
              disabled={isAnimating}
            />
            {!isAnimating && <div className="w-2 h-5 bg-white ml-1 animate-pulse"></div>}
          </div>
        </div>
      )}
    </div>
  );
}