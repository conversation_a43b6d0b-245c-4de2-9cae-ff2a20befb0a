// ABOUTME: This is the main stateful component for the terminal experience.
// ABOUTME: It manages user input, command history, and renders MDX content.

"use client";

import { useState, useEffect, useRef } from 'react';
import { processCommand, neofetchData, creditsData, CommandOutput } from '@/lib/command-engine';
import { ConnectFormData } from '@/lib/types';
import { useMDXComponent } from 'next-contentlayer/hooks'; // <-- Import the hook

// --- HELPER COMPONENTS --- //

// This new component will render our MDX content
const MdxContent = ({ code }: { code: string }) => {
  const MDXComponent = useMDXComponent(code);
  return <MDXComponent />;
};

const Typewriter = ({ text, onComplete }: { text: string, onComplete?: () => void }) => {
  const [displayedText, setDisplayedText] = useState('');
  useEffect(() => {
    setDisplayedText('');
    let i = 0;
    const intervalId = setInterval(() => {
      setDisplayedText(prev => prev + text.charAt(i));
      i++;
      if (i >= text.length) {
        clearInterval(intervalId);
        onComplete?.();
      }
    }, 20);
    return () => clearInterval(intervalId);
  }, [text, onComplete]);
  return <>{displayedText}</>;
};

// ... (NeofetchOutput, CreditsOutput, ListOutput components remain the same) ...
const NeofetchOutput = ({ data }: { data: typeof neofetchData }) => ( <div className="flex gap-4"> <div className="text-purple-400 font-bold text-5xl">ES</div> <div> <p><span className="text-purple-400 font-bold">{data.name}</span></p> <p>--------------------</p> <p><span className="font-bold">OS:</span> {data.os}</p> <p><span className="font-bold">Stack:</span> {data.stack}</p> <p><span className="font-bold">Services:</span> {data.services}</p> <p><span className="font-bold">Philosophy:</span> {data.philosophy}</p> </div> </div> );
const CreditsOutput = ({ data }: { data: typeof creditsData }) => ( <div> <p className="font-bold">{data.title}</p> <div className="flex flex-wrap gap-x-4"> {data.tools.map(tool => ( <a key={tool.name} href={tool.url} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline"> {tool.name} </a> ))} </div> </div> );
const ListOutput = ({ items, onCommand, onHover }: { items: any[], onCommand: (cmd: string) => void, onHover: (cmd: string | null) => void }) => { const [hoveredItem, setHoveredItem] = useState<string | null>(null); const getCommand = (item: any) => { if (item.type === 'directory') return 'services'; return item.name; }; return ( <div className="flex flex-col"> {items.map(item => ( <div key={item.name} className="flex items-center cursor-pointer" onClick={() => onCommand(getCommand(item))} onMouseEnter={() => { setHoveredItem(item.name); onHover(getCommand(item)); }} onMouseLeave={() => { setHoveredItem(null); onHover(null); }} > <span className={`w-4 transition-opacity duration-200 ${hoveredItem === item.name ? 'opacity-100' : 'opacity-0'}`}> {'>'} </span> <span className={`${item.type === 'directory' ? 'text-blue-400' : 'text-orange-400'}`}> {item.name} </span> <span className={`w-4 transition-opacity duration-200 ${hoveredItem === item.name ? 'opacity-100' : 'opacity-0'}`}> {'<'} </span> </div> ))} </div> ); };

// Update OutputLine to handle the new MDX content type
const OutputLine = ({ data, onCommand, onHover }: { data: any, onCommand: any, onHover: any }) => {
  if (typeof data === 'string') return <Typewriter text={data} />;
  if (Array.isArray(data)) return <ListOutput items={data} onCommand={onCommand} onHover={onHover} />;
  if (data?.type === 'NEOFETCH') return <NeofetchOutput data={data} />;
  if (data?.type === 'CREDITS') return <CreditsOutput data={data} />;
  if (data?.type === 'MDX_CONTENT') return <MdxContent code={data.code} />;
  return data;
};

// --- MAIN COMPONENT --- //
// ... (The rest of the EspiraiOS component remains exactly the same) ...
export default function EspiraiOS() {
  const [typedInput, setTypedInput] = useState('');
  const [hoveredCommand, setHoveredCommand] = useState<string | null>(null);
  const [output, setOutput] = useState<React.ReactNode[]>([]);
  const [showPrompt, setShowPrompt] = useState(false);
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  const [isConnecting, setIsConnecting] = useState(false);
  const [connectStep, setConnectStep] = useState(0);
  const [connectAnswers, setConnectAnswers] = useState<Partial<ConnectFormData>>({});

  const endOfOutputRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const connectQuestions = [
    { key: 'name', question: 'What name should we use?' },
    { key: 'email', question: () => `And your email, ${connectAnswers.name}?` },
    { key: 'challenge', question: 'Tell us about the challenge or opportunity on your mind.' },
    { key: 'area', question: 'From what you\'ve described, does it fall into one of these areas? (automation, data, custom-ai, etc.)' },
    { key: 'interest', question: 'A key part of our model is result-based pricing—we\'re incentivized by the performance of what we build. Is exploring an accountable partnership like this of interest? (yes/no)' },
  ];

  const handleCommandSubmit = (commandToSubmit: string) => {
    if (isConnecting) {
      handleConnectSubmit(commandToSubmit);
    } else {
      handleTerminalCommand(commandToSubmit);
    }
  };

  const handleTerminalCommand = (command: string) => {
    if (command.trim() === '') return;
    setCommandHistory(prev => [command, ...prev]);
    setHistoryIndex(-1);

    const commandHistoryLine = (
      <div className="flex">
        <span className="text-gray-500">/dev/agents> </span>
        <span>{command}</span>
      </div>
    );
    const result = processCommand(command);

    if (result.__signal__ === 'CLEAR') {
      setOutput([]);
    } else if (result.__signal__ === 'ENGAGE_CONNECT') {
      setOutput(prev => [...prev, commandHistoryLine, `[+] Initializing secure connection to EspirAI Lab...`]);
      setIsConnecting(true);
      setConnectStep(0);
      setConnectAnswers({});
    } else {
      setOutput(prev => [...prev, commandHistoryLine, result.data]);
    }

    setTypedInput('');
    setHoveredCommand(null);
  };

  const handleConnectSubmit = async (answer: string) => {
    if (!answer.trim()) return;

    const currentQuestion = connectQuestions[connectStep];
    const questionText = typeof currentQuestion.question === 'function' ? currentQuestion.question() : currentQuestion.question;

    const questionLine = (
      <p className="text-gray-300">
        <span className="text-cyan-400 mr-2">{`[${connectStep + 1}/${connectQuestions.length}]`}</span>
        {questionText}
      </p>
    );
    const answerLine = (
      <p className="text-white">
        <span className="text-green-400 mr-2">{'>'}</span>
        {answer}
      </p>
    );

    setOutput(prev => [...prev, questionLine, answerLine]);

    const newAnswers = { ...connectAnswers, [currentQuestion.key]: answer };
    setConnectAnswers(newAnswers);

    setTypedInput('');

    if (connectStep < connectQuestions.length - 1) {
      setConnectStep(connectStep + 1);
    } else {
      setIsConnecting(false);
      setOutput(prev => [...prev, "Submitting your request..."]);
      try {
        const response = await fetch('/api/connect', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(newAnswers),
        });
        if (!response.ok) throw new Error('API request failed');
        const successMessage = `[+] Got it. We thrive on accountability. Your inquiry has been received. We'll review your notes and be in touch at ${newAnswers.email} within one business day.`;
        setOutput(prev => [...prev, successMessage]);
      } catch (error) {
        console.error('Failed to submit connection form:', error);
        const errorMessage = `[!] Connection failed. Please try again later or contact us directly.`;
        setOutput(prev => [...prev, errorMessage]);
      }
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    const currentInput = hoveredCommand || typedInput;
    if (event.key === 'Enter') {
      event.preventDefault();
      handleCommandSubmit(currentInput);
    } else if (event.key === 'ArrowUp' && !isConnecting) {
      event.preventDefault();
      const newIndex = Math.min(historyIndex + 1, commandHistory.length - 1);
      if (newIndex >= 0) {
        setHistoryIndex(newIndex);
        setTypedInput(commandHistory[newIndex]);
        setHoveredCommand(null);
      }
    } else if (event.key === 'ArrowDown' && !isConnecting) {
      event.preventDefault();
      const newIndex = Math.max(historyIndex - 1, -1);
      setHistoryIndex(newIndex);
      setTypedInput(newIndex >= 0 ? commandHistory[newIndex] : '');
      setHoveredCommand(null);
    }
  };

  useEffect(() => {
    const motd = "We're building the next-gen operating system for AI agents.";
    const handleMotdComplete = () => {
      setShowPrompt(true);
      setHoveredCommand('ls');
    };
    const motdNode = <Typewriter text={motd} onComplete={handleMotdComplete} />;
    setOutput([motdNode]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    endOfOutputRef.current?.scrollIntoView({ behavior: 'auto' });
  }, [output]);

  useEffect(() => {
    inputRef.current?.focus();
  }, [isConnecting, showPrompt]);

  const renderPrompt = () => {
    if (isConnecting) {
      const currentQuestion = connectQuestions[connectStep];
      const questionText = typeof currentQuestion.question === 'function' ? currentQuestion.question() : currentQuestion.question;
      return (
        <label htmlFor="terminal-input" className="text-cyan-400 mr-2 whitespace-nowrap">
          {`[${connectStep + 1}/${connectQuestions.length}] ${questionText} >`}
        </label>
      );
    }
    return <span className="text-gray-500">/dev/agents> </span>;
  };

  const displayInput = hoveredCommand || typedInput;

  return (
    <div className="flex flex-col h-screen p-2 md:p-4" onClick={() => inputRef.current?.focus()}>
      <div className="flex-1 overflow-y-auto flex flex-col justify-end">
        <div className="whitespace-pre-wrap">
          {output.map((line, index) => (
            <div key={index} className="mb-2">
              <OutputLine data={line} onCommand={handleTerminalCommand} onHover={setHoveredCommand} />
            </div>
          ))}
        </div>
        <div ref={endOfOutputRef} />
      </div>

      {showPrompt && (
        <div className="flex flex-shrink-0 items-center">
          {renderPrompt()}
          <div className="flex-1">{displayInput}</div>
          <input
            id="terminal-input"
            ref={inputRef}
            className="absolute opacity-0"
            value={typedInput}
            onChange={(e) => {
              setTypedInput(e.target.value);
              setHoveredCommand(null);
            }}
            onKeyDown={handleKeyDown}
            autoFocus
          />
          <div className="w-3 h-4 border-2 border-orange-400 rounded-sm animate-pulse"></div>
        </div>
      )}
    </div>
  );
}