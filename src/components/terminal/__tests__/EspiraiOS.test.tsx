// ABOUTME: This file contains unit tests for the main EspiraiOS terminal component.
// ABOUTME: It uses Jest and React Testing Library to verify component behavior.

import React from 'react';
import { render, screen, act } from '@testing-library/react';
import EspiraiOS from '../EspiraiOS';

// We mock the command-engine because we are only testing the component's rendering logic,
// not the command processing itself. This makes our test faster and more focused.
jest.mock('../../../lib/command-engine', () => ({
  processCommand: jest.fn(() => ({ data: 'mocked command output' })),
  mdxComponents: {},
}));

describe('EspiraiOS', () => {
  // This test specifically targets the "doubled output" bug in the intro sequence.
  it('should display the intro sequence without duplicating lines', () => {
    // We use fake timers to control the passage of time for the Typewriter's setInterval.
    jest.useFakeTimers();

    render(<EspiraiOS />);

    // Fast-forward time until all setIntervals in the Typewriter have completed.
    act(() => {
      jest.runAllTimers();
    });

    // The first line of the welcome message.
    const welcomeLine1 = "We're an AI-native software engineering firm solving problems that actually matter.";

    // After the animation, the completed static text should be on the screen.
    // We expect to find it EXACTLY once.
    const line1Elements = screen.getAllByText(welcomeLine1);
    expect(line1Elements).toHaveLength(1);
  });
});