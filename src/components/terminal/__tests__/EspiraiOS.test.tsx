// ABOUTME: This file contains unit tests for the main EspiraiOS terminal component.
// ABOUTME: It uses Jest and React Testing Library to verify component behavior.

import React from 'react';
import { render, screen, act, waitFor } from '@testing-library/react';
import EspiraiOS from '../EspiraiOS';

// We mock the command-engine because we are only testing the component's rendering logic,
// not the command processing itself. This makes our test faster and more focused.
jest.mock('../../../lib/command-engine', () => ({
  processCommand: jest.fn(() => ({ data: 'mocked command output' })),
  mdxComponents: {},
}));

describe('EspiraiOS', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // This test specifically targets the "doubled output" bug in the intro sequence.
  it('should display the intro sequence without duplicating lines', async () => {
    // We use fake timers to control the passage of time for the Typewriter's setInterval.
    jest.useFakeTimers();

    render(<EspiraiOS />);

    // Fast-forward time until all setIntervals in the Typewriter have completed.
    act(() => {
      jest.runAllTimers();
    });

    // The first line of the welcome message.
    const welcomeLine1 = "We're an AI-native software engineering firm solving problems that actually matter.";

    // Wait for the content to appear and check it's not duplicated
    await waitFor(() => {
      const line1Elements = screen.queryAllByText(welcomeLine1);
      expect(line1Elements.length).toBeLessThanOrEqual(1);
    });

    // Restore real timers
    jest.useRealTimers();
  });

  it('should render the terminal interface', () => {
    const { container } = render(<EspiraiOS />);

    // Check that the main container is rendered with expected classes
    const terminalContainer = container.firstChild as HTMLElement;
    expect(terminalContainer).toBeDefined();
    expect(terminalContainer.className).toContain('flex');
    expect(terminalContainer.className).toContain('flex-col');
    expect(terminalContainer.className).toContain('h-screen');
  });

  it('should start the intro sequence immediately', () => {
    render(<EspiraiOS />);

    // The intro sequence should start immediately, so we should see a typewriter component
    // Even before animations complete, the container should be present
    const outputContainer = document.querySelector('.whitespace-pre-wrap');
    expect(outputContainer).toBeTruthy();
  });
});