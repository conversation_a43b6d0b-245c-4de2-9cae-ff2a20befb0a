// ABOUTME: This component renders the multi-step "connect" questionnaire.
// ABOUTME: It manages the conversational flow and calls back on completion.

'use client';

import { useState, useEffect, useRef } from 'react';
import { ConnectFormData } from '@/lib/types';

// Define the structure of a single historical step in the conversation
type ConversationStep = {
  question: string;
  answer: string;
};

// Define the props for our component
interface ConnectFormProps {
  onComplete: (data: ConnectFormData) => void;
}

const ConnectForm = ({ onComplete }: ConnectFormProps) => {
  // State for the form data we are collecting
  const [answers, setAnswers] = useState<ConnectFormData>({
    name: '',
    email: '',
    challenge: '',
    area: '',
    interest: '',
  });

  // State for the UI's conversational flow
  const [step, setStep] = useState(1);
  const [currentInput, setCurrentInput] = useState('');
  const [conversationHistory, setConversationHistory] = useState<ConversationStep[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);

  // Use a function for question 2 to inject the user's name
  const questions = [
    '', // Placeholder for index 0 to align with step number
    'What name should we use?',
    () => `And your email, ${answers.name}?`,
    'Tell us about the challenge or opportunity on your mind.',
    'From what you\'ve described, does it fall into one of these areas? (automation, data, custom-ai, etc.)',
    'A key part of our model is result-based pricing—we\'re incentivized by the performance of what we build. Is exploring an accountable partnership like this of interest? (yes/no)',
  ];

  const getCurrentQuestion = () => {
    const questionOrFn = questions[step];
    return typeof questionOrFn === 'function' ? questionOrFn() : questionOrFn;
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentInput.trim()) return; // Ignore empty submissions

    // Record the completed step for display
    const newHistoryEntry: ConversationStep = {
      question: getCurrentQuestion(),
      answer: currentInput,
    };
    setConversationHistory([...conversationHistory, newHistoryEntry]);

    // Store the answer
    const newAnswers = { ...answers };
    switch (step) {
      case 1: newAnswers.name = currentInput; break;
      case 2: newAnswers.email = currentInput; break;
      case 3: newAnswers.challenge = currentInput; break;
      case 4: newAnswers.area = currentInput; break;
      case 5: newAnswers.interest = currentInput.toLowerCase(); break;
    }
    setAnswers(newAnswers);

    // Clear input and advance to the next step, or finish
    setCurrentInput('');
    if (step < questions.length - 1) {
      setStep(step + 1);
    } else {
      onComplete(newAnswers); // Final step is done, call the callback
    }
  };
  
  // Auto-focus the input field on load and when the step changes
  useEffect(() => {
    inputRef.current?.focus();
  }, [step]);

  return (
    <div className="w-full flex-grow flex flex-col font-mono p-4">
      {/* This div will hold the completed conversation history */}
      <div className="flex-grow">
        {conversationHistory.length === 0 && (
            <p className="text-gray-300">[+] Initializing secure connection to EspirAI Lab...</p>
        )}
        
        {conversationHistory.map((entry, index) => (
          <div key={`history-${index}`} className="mb-2">
            <p className="text-gray-300">
              <span className="text-cyan-400 mr-2">{`[${index + 1}/${questions.length -1}]`}</span>
              {entry.question}
            </p>
            <p className="text-white">
                <span className="text-green-400 mr-2">{'>'}</span>
                {entry.answer}
            </p>
          </div>
        ))}
      </div>

      {/* This form contains the current question and the user input */}
      {step < questions.length && (
        <form onSubmit={handleFormSubmit} className="flex w-full">
          <label htmlFor="connect-input" className="text-cyan-400 mr-2 whitespace-nowrap">
            {`[${step}/${questions.length - 1}] ${getCurrentQuestion()} >`}
          </label>
          <input
            id="connect-input"
            ref={inputRef}
            type="text"
            value={currentInput}
            onChange={(e) => setCurrentInput(e.target.value)}
            className="flex-grow bg-transparent border-none text-white outline-none w-full"
            autoComplete="off"
            autoCapitalize="off"
            spellCheck="false"
          />
        </form>
      )}
    </div>
  );
};

export default ConnectForm;