@tailwind base;
@tailwind components;
@tailwind utilities;

/* Add smooth scrolling to the entire site */
html {
  scroll-behavior: smooth;
}

/* Ensure body takes full height and allows scrolling */
body {
  min-height: 100vh;
  overflow-y: auto;
  background-color: #0a0a0a;
  background-image: linear-gradient(125deg, #0a0a0a 0%, #121212 100%);
  color: #f0f0f0;
  font-family: var(--font-monaspace), 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace;
  font-size: 15px;
  line-height: 1.7;
  letter-spacing: -0.02em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Refined terminal typography inspired by sdsa.ai */
body {
  background-color: #0a0a0a;
  background-image: linear-gradient(125deg, #0a0a0a 0%, #121212 100%);
  color: #f0f0f0;
  font-family: var(--font-monaspace), 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace;
  font-size: 15px;
  line-height: 1.7;
  letter-spacing: -0.02em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Terminal prompt styling */
.terminal-prompt {
  color: #8f8f8f;
  margin-right: 0.5rem;
}

/* Command styling */
.terminal-command {
  color: #ffffff;
  font-weight: 500;
}

/* Directory path styling */
.terminal-path {
  color: #4a9eff;
  font-weight: 500;
}

/* Terminal output styling */
.terminal-output {
  margin: 1.5rem 0;
  line-height: 1.8;
}

/* Command list styling */
.command-list {
  margin: 0.75rem 0;
}

.command-list-item {
  margin: 0.5rem 0;
  color: #ff6e40;
  transition: color 0.2s ease;
}

.command-list-item:hover {
  color: #ff8a65;
}

/* Responsive typography */
@media (min-width: 768px) {
  body {
    font-size: 16px;
    line-height: 1.7;
  }
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #0a0a0a;
}

::-webkit-scrollbar-thumb {
  background: #333;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Selection styling */
::selection {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

/* Prose styling for static pages */
.prose {
  color: #ffffff;
  max-width: none;
}

.prose h1 {
  color: #ffffff;
  font-weight: 700;
  font-size: 2.25rem;
  line-height: 2.5rem;
  margin-bottom: 1rem;
}

.prose h2 {
  color: #ffffff;
  font-weight: 600;
  font-size: 1.875rem;
  line-height: 2.25rem;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose h3 {
  color: #ffffff;
  font-weight: 600;
  font-size: 1.5rem;
  line-height: 2rem;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.prose p {
  color: #e5e7eb;
  margin-bottom: 1rem;
  line-height: 1.75;
}

.prose ul {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.prose li {
  color: #e5e7eb;
  margin-bottom: 0.5rem;
}

.prose strong {
  color: #ffffff;
  font-weight: 600;
}

.prose code {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

.prose pre {
  background-color: #111827;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.prose a {
  color: #60a5fa;
  text-decoration: none;
}

.prose a:hover {
  color: #93c5fd;
  text-decoration: underline;
}
