import { notFound } from 'next/navigation';
import { allPages } from 'contentlayer/generated';
import { useMDXComponent } from 'next-contentlayer/hooks';
import { mdxComponents } from '@/lib/command-engine';
import type { Metadata } from 'next';

interface PageProps {
  params: {
    slug: string[];
  };
}

// Generate static params for all pages except the root
export async function generateStaticParams() {
  return allPages
    .filter((page) => page.slug !== '') // Exclude root page
    .map((page) => ({
      slug: page.slug.split('/'),
    }));
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const slug = params.slug?.join('/') || '';
  const page = allPages.find((page) => page.slug === slug);

  if (!page) {
    return {
      title: 'Page Not Found - EspirAI Labs',
    };
  }

  return {
    title: `${page.title} - EspirAI Labs`,
    description: `${page.title} - Building the next-gen operating system for AI agents`,
    openGraph: {
      title: `${page.title} - EspirAI Labs`,
      description: `${page.title} - Building the next-gen operating system for AI agents`,
      type: 'website',
      url: `https://espirai.com/${slug}`,
    },
    twitter: {
      card: 'summary_large_image',
      title: `${page.title} - EspirAI Labs`,
      description: `${page.title} - Building the next-gen operating system for AI agents`,
    },
  };
}

export default function StaticPage({ params }: PageProps) {
  const slug = params.slug?.join('/') || '';
  const page = allPages.find((page) => page.slug === slug);

  if (!page) {
    notFound();
  }

  const MDXContent = useMDXComponent(page.body.code);

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="max-w-4xl mx-auto px-6 py-12">
        {/* Header */}
        <header className="mb-12">
          <nav className="mb-8">
            <a 
              href="/" 
              className="text-gray-400 hover:text-white transition-colors"
            >
              ← Back to Terminal
            </a>
          </nav>
          <h1 className="text-4xl font-bold mb-4">{page.title}</h1>
          <div className="text-gray-400">
            EspirAI Labs - Building the next-gen operating system for AI agents
          </div>
        </header>

        {/* Content */}
        <article className="prose prose-invert prose-lg max-w-none">
          <MDXContent components={mdxComponents} />
        </article>

        {/* Footer */}
        <footer className="mt-16 pt-8 border-t border-gray-800">
          <div className="text-gray-400 text-sm">
            <p>Want to experience the full interactive terminal? <a href="/" className="text-white hover:underline">Visit our main site</a></p>
            <p className="mt-2">Ready to build something extraordinary? <a href="/#connect" className="text-white hover:underline">Get in touch</a></p>
          </div>
        </footer>
      </div>
    </div>
  );
}
