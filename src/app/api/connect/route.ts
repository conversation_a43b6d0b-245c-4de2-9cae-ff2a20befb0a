// ABOUTME: This file defines the API endpoint for the 'connect' command.
// ABOUTME: It handles the backend logic for the lead generation funnel.

import { NextResponse } from 'next/server';

/**
 * Handles POST requests to the /api/connect endpoint.
 * This is the entry point for our lead generation funnel.
 */
export async function POST(request: Request) {
  try {
    const body = await request.json();
    
    // For now, we'll just log the data to our server console.
    // In the future, this is where we will save to a database and send an email.
    console.log('✅ Lead Received:', body);

    // You can add validation here later (e.g., check for email format)

    return NextResponse.json({ message: 'Lead successfully submitted.' }, { status: 200 });

  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json({ message: 'Error processing request.' }, { status: 500 });
  }
}