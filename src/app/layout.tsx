// src/app/layout.tsx

import type { Metadata } from 'next';
import { monaspace } from '@/lib/fonts'; // <-- Import your font
import './globals.css';

export const metadata: Metadata = {
  title: 'EspirAI Labs - Building the next-gen operating system for AI agents',
  description: 'We\'re building the infrastructure that makes it possible to build robust, agentic applications. Modern AI will fundamentally change how people use software in their daily lives.',
  keywords: ['AI agents', 'artificial intelligence', 'software engineering', 'AI platform', 'machine learning'],
  authors: [{ name: 'EspirAI Labs' }],
  openGraph: {
    title: 'EspirAI Labs - Building the next-gen operating system for AI agents',
    description: 'We\'re building the infrastructure that makes it possible to build robust, agentic applications.',
    type: 'website',
    url: 'https://espirai.com',
    siteName: 'EspirAI Labs',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'EspirAI Labs - Building the next-gen operating system for AI agents',
    description: 'We\'re building the infrastructure that makes it possible to build robust, agentic applications.',
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      {/* Apply the font's CSS variable to the body */}
      <body className={monaspace.variable}>
        {children}
      </body>
    </html>
  );
}