// src/app/layout.tsx

import type { Metadata } from 'next';
import { monaspace } from '@/lib/fonts'; // <-- Import your font
import './globals.css';

export const metadata: Metadata = {
  title: 'Espirai Labs',
  description: 'The next-gen operating system for AI agents.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      {/* Apply the font's CSS variable to the body */}
      <body className={monaspace.variable}>
        {children}
      </body>
    </html>
  );
}