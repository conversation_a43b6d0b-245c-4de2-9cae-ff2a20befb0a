import { notFound } from 'next/navigation';
import { allPages } from 'contentlayer/generated';
import { useMDXComponent } from 'next-contentlayer/hooks';
import { mdxComponents } from '@/lib/command-engine';
import type { Metadata } from 'next';

interface ServiceProps {
  params: {
    slug: string;
  };
}

// Generate static params for services
export async function generateStaticParams() {
  return allPages
    .filter((page) => page.slug.startsWith('services/'))
    .map((page) => ({
      slug: page.slug.replace('services/', ''),
    }));
}

// Generate metadata for SEO
export async function generateMetadata({ params }: ServiceProps): Promise<Metadata> {
  const page = allPages.find((page) => page.slug === `services/${params.slug}`);

  if (!page) {
    return {
      title: 'Service Not Found - EspirAI Labs',
    };
  }

  return {
    title: `${page.title} - EspirAI Labs`,
    description: `${page.title} - Professional AI and software engineering services`,
    openGraph: {
      title: `${page.title} - EspirAI Labs`,
      description: `${page.title} - Professional AI and software engineering services`,
      type: 'website',
      url: `https://espirai.com/services/${params.slug}`,
    },
  };
}

export default function ServicePage({ params }: ServiceProps) {
  const page = allPages.find((page) => page.slug === `services/${params.slug}`);

  if (!page) {
    notFound();
  }

  const MDXContent = useMDXComponent(page.body.code);

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="max-w-4xl mx-auto px-6 py-12">
        <header className="mb-12">
          <nav className="mb-8">
            <a 
              href="/" 
              className="text-gray-400 hover:text-white transition-colors"
            >
              ← Back to Terminal
            </a>
          </nav>
          <h1 className="text-4xl font-bold mb-4">{page.title}</h1>
          <div className="text-gray-400">
            EspirAI Labs - Professional AI and Software Engineering Services
          </div>
        </header>

        <article className="prose prose-invert prose-lg max-w-none">
          <MDXContent components={mdxComponents} />
        </article>

        <footer className="mt-16 pt-8 border-t border-gray-800">
          <div className="text-gray-400 text-sm">
            <p>Want to experience the full interactive terminal? <a href="/" className="text-white hover:underline">Visit our main site</a></p>
            <p className="mt-2">Ready to build something extraordinary? <a href="/#connect" className="text-white hover:underline">Get in touch</a></p>
          </div>
        </footer>
      </div>
    </div>
  );
}
