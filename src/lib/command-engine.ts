// ABOUTME: This file contains the core logic for parsing commands for the guided terminal.
// ABOUTME: It uses Contentlayer to fetch and process MDX content files.

import { allPages, Page } from 'contentlayer/generated';
import Accordion from '@/components/content/Accordion';

export const mdxComponents = {
  Accordion,
};

export interface CommandOutput {
  __signal__?: 'CLEAR' | 'ENGAGE_CONNECT';
  data?: any;
}

const findPage = (slug: string): Page | undefined => {
  return allPages.find(p => p.slug === slug);
};

export function processCommand(command: string): CommandOutput {
  const [cmd, ...args] = command.trim().split(/\s+/);
  if (!cmd) return { data: '' };
  const lowerCaseCmd = cmd.toLowerCase();

  switch (lowerCaseCmd) {
    case 'help':
      return { data: 'Commands: ls, cat <page>, about, jobs, who, services, neofetch, connect, help, clear' };
    case 'neofetch':
      return { data: "Neofetch data coming soon." }; // Placeholder
    case 'credits':
      return { data: "Credits data coming soon." }; // Placeholder
    case 'clear':
      return { __signal__: 'CLEAR' };
    case 'connect':
      return { __signal__: 'ENGAGE_CONNECT' };

    case 'ls':
      const topLevelPages = allPages
        .filter(p => p.slug.startsWith('pages/'))
        .map(p => ({ name: p.slug.replace('pages/', ''), type: 'file' }));
      const servicesDir = { name: 'services', type: 'directory' };
      const newsDir = { name: 'news', type: 'directory' }; // Add news directory
      return { data: [...topLevelPages, servicesDir, newsDir] };
    
    case 'services':
      const servicePages = allPages
        .filter(p => p.slug.startsWith('services/'))
        .map(p => ({ name: p.slug.replace('services/', ''), type: 'service' }));
      return { data: servicePages };

    case 'news':
       const newsPages = allPages
        .filter(p => p.slug.startsWith('news/'))
        .map(p => ({ name: p.slug.replace('news/', ''), type: 'news' }));
      return { data: newsPages };

    // This handles commands like `about`, which are shortcuts for `cat pages/about`
    default:
      const pageAsCommand = findPage(`pages/${lowerCaseCmd}`) || findPage(`services/${lowerCaseCmd}`);
      if (pageAsCommand) {
        return { data: { type: 'MDX_CONTENT', code: pageAsCommand.body.code, components: mdxComponents } };
      }

      // Check for `cat` command, e.g., `cat pages/about` or `cat news/bokoo-acquisition`
      if (lowerCaseCmd === 'cat' && args.length > 0) {
        const slug = args[0];
        const page = findPage(slug);
        if (page) {
          return { data: { type: 'MDX_CONTENT', code: page.body.code, components: mdxComponents } };
        }
        return { data: `cat: ${slug}: No such file or directory` };
      }

      return { data: `command not found: ${cmd}` };
  }
}