// ABOUTME: This file contains the core logic for parsing commands for the guided terminal.
// ABOUTME: It uses Contentlayer to fetch and process MDX content files.

import { allPages, Page } from 'contentlayer/generated';
import Accordion from '@/components/content/Accordion';
import CodePlayground from '@/components/content/CodePlayground';

export const mdxComponents = {
  Accordion,
  CodePlayground,
};

export interface CommandOutput {
  __signal__?: 'CLEAR' | 'ENGAGE_CONNECT';
  data?: any;
}

const findPage = (slug: string): Page | undefined => {
  return allPages.find(p => p.slug === slug);
};

export function processCommand(command: string): CommandOutput {
  const [cmd, ...args] = command.trim().split(/\s+/);
  if (!cmd) return { data: '' };
  const lowerCaseCmd = cmd.toLowerCase();

  switch (lowerCaseCmd) {
    case 'help':
      return {
        data: [
          { name: 'about', type: 'command' },
          { name: 'demo', type: 'command' },
          { name: 'jobs', type: 'command' },
          { name: 'who', type: 'command' },
          { name: 'services', type: 'directory' },
          { name: 'news', type: 'directory' },
          { name: 'neofetch', type: 'command' },
          { name: 'fortune', type: 'command' },
          { name: 'cowsay', type: 'command' },
          { name: 'connect', type: 'command' },
          { name: 'clear', type: 'command' },
        ]
      };
    case 'neofetch':
      return {
        data: `
╭─────────────────────────────────────╮
│  EspiraiOS v1.6.0                   │
│  ─────────────────────────────────   │
│  OS: Next.js 15.3.3                 │
│  Runtime: React 19                  │
│  Language: TypeScript               │
│  Styling: Tailwind CSS 4.1.8        │
│  Content: Contentlayer + MDX        │
│  Testing: Jest + React Testing Lib  │
│  ─────────────────────────────────   │
│  Status: Online                     │
│  Uptime: ${new Date().toLocaleString()}                │
│  Location: /dev/espirai             │
╰─────────────────────────────────────╯
        `.trim()
      };
    case 'fortune':
      const fortunes = [
        "A journey of a thousand miles begins with a single step.",
        "The best way to predict the future is to create it.",
        "Code is like humor. When you have to explain it, it's bad.",
        "There is no place like 127.0.0.1.",
        "To err is human, but to really foul things up you need a computer.",
        "Have you tried turning it off and on again?",
        "It's not a bug, it's an undocumented feature.",
        "Modern AI will fundamentally change how people use software.",
        "The future belongs to those who build it.",
      ];
      const randomFortune = fortunes[Math.floor(Math.random() * fortunes.length)];
      return { data: randomFortune };
    case 'cowsay':
      const message = args.slice(1).join(' ') || 'Building the future of AI';
      const cow = [
        ` ${"_".repeat(message.length + 2)}`,
        `< ${message} >`,
        ` ${"-".repeat(message.length + 2)}`,
        "        \\   ^__^",
        "         \\  (oo)\\_______",
        "            (__)\\       )\\/\\",
        "                ||----w |",
        "                ||     ||"
      ];
      return { data: cow.join('\n') };
    case 'credits':
      return { data: "Built with Next.js, React, TypeScript, and Tailwind CSS." };
    case 'clear':
      return { __signal__: 'CLEAR' };
    case 'connect':
      return { __signal__: 'ENGAGE_CONNECT' };

    case 'ls':
      const topLevelPages = allPages
        .filter(p => p.slug.startsWith('pages/'))
        .map(p => ({ name: p.slug.replace('pages/', ''), type: 'file' }));
      const servicesDir = { name: 'services', type: 'directory' };
      const newsDir = { name: 'news', type: 'directory' }; // Add news directory
      return { data: [...topLevelPages, servicesDir, newsDir] };
    
    case 'services':
      const servicePages = allPages
        .filter(p => p.slug.startsWith('services/'))
        .map(p => ({ name: p.slug.replace('services/', ''), type: 'service' }));
      return { data: servicePages };

    case 'news':
       const newsPages = allPages
        .filter(p => p.slug.startsWith('news/'))
        .map(p => ({ name: p.slug.replace('news/', ''), type: 'news' }));
      return { data: newsPages };

    // This handles commands like `about`, which are shortcuts for `cat pages/about`
    default:
      const pageAsCommand = findPage(`pages/${lowerCaseCmd}`) || findPage(`services/${lowerCaseCmd}`);
      if (pageAsCommand) {
        return { data: { type: 'MDX_CONTENT', code: pageAsCommand.body.code, components: mdxComponents } };
      }

      // Check for `cat` command, e.g., `cat pages/about` or `cat news/bokoo-acquisition`
      if (lowerCaseCmd === 'cat' && args.length > 0) {
        const slug = args[0];
        const page = findPage(slug);
        if (page) {
          return { data: { type: 'MDX_CONTENT', code: page.body.code, components: mdxComponents } };
        }
        return { data: `cat: ${slug}: No such file or directory` };
      }

      return { data: `command not found: ${cmd}` };
  }
}