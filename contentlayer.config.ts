// ABOUTME: This file configures Contentlayer to process our MDX content.
// ABOUTME: It defines the schema and generates typed data from our content files.

import { defineDocumentType, makeSource } from 'contentlayer/source-files';

// Define a schema for a generic "Page" of content.
// This will apply to about, services, jobs, etc.
export const Page = defineDocumentType(() => ({
  name: 'Page',
  filePathPattern: `**/*.mdx`, // Look for all .mdx files in all subdirectories
  contentType: 'mdx',
  fields: {
    title: {
      type: 'string',
      description: 'The title of the page',
      required: true,
    },
  },
  computedFields: {
    // We need a unique slug to identify each page.
    // e.g., 'services/data-engineering.mdx' becomes 'services/data-engineering'
    slug: {
      type: 'string',
      resolve: (doc) => doc._raw.flattenedPath,
    },
  },
}));

export default makeSource({
  contentDirPath: 'content', // The directory where our MDX files will live
  documentTypes: [Page],
});