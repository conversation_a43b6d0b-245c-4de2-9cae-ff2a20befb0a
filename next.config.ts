// ABOUTME: This is the Next.js configuration file.
// ABOUTME: It's wrapped with the Contentlayer plugin to enable MDX processing.

import { withContentlayer } from 'next-contentlayer';

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Your existing Next.js config options can go here if you have any.
  // For example:
  // reactStrictMode: true,
};

export default withContentlayer(nextConfig);