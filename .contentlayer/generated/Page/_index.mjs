// NOTE This file is auto-generated by Contentlayer

import services__aiDevelopmentMdx from './services__ai-development.mdx.json' assert { type: 'json' }
import services__dataEngineeringMdx from './services__data-engineering.mdx.json' assert { type: 'json' }
import pages__aboutMdx from './pages__about.mdx.json' assert { type: 'json' }
import pages__jobsMdx from './pages__jobs.mdx.json' assert { type: 'json' }
import pages__whoMdx from './pages__who.mdx.json' assert { type: 'json' }
import news__bokooAcquisitionMdx from './news__bokoo-acquisition.mdx.json' assert { type: 'json' }

export const allPages = [services__aiDevelopmentMdx, services__dataEngineeringMdx, pages__aboutMdx, pages__jobsMdx, pages__whoMdx, news__bokooAcquisitionMdx]
