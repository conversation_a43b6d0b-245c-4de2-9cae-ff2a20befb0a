{"title": "Data Engineering", "body": {"raw": "\nWe build robust, scalable data infrastructure that powers intelligent applications.\n\nOur data engineering solutions ensure your AI systems have access to clean, reliable, and real-time data. We design pipelines that can handle massive scale while maintaining data quality and governance.\n\nOur expertise includes:\n- Real-time data streaming and processing\n- Data warehouse and lake architecture\n- ETL/ELT pipeline development\n- Data quality monitoring and validation\n- Cloud-native data solutions", "code": "var Component=(()=>{var ie=Object.create;var p=Object.defineProperty;var oe=Object.getOwnPropertyDescriptor;var se=Object.getOwnPropertyNames;var ce=Object.getPrototypeOf,le=Object.prototype.hasOwnProperty;var N=(t,n)=>()=>(n||t((n={exports:{}}).exports,n),n.exports),ue=(t,n)=>{for(var c in n)p(t,c,{get:n[c],enumerable:!0})},P=(t,n,c,b)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let l of se(n))!le.call(t,l)&&l!==c&&p(t,l,{get:()=>n[l],enumerable:!(b=oe(n,l))||b.enumerable});return t};var de=(t,n,c)=>(c=t!=null?ie(ce(t)):{},P(n||!t||!t.__esModule?p(c,\"default\",{value:t,enumerable:!0}):c,t)),fe=t=>P(p({},\"__esModule\",{value:!0}),t);var U=N((ve,C)=>{C.exports=React});var D=N(h=>{\"use strict\";(function(){function t(e){if(e==null)return null;if(typeof e==\"function\")return e.$$typeof===ee?null:e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case E:return\"Fragment\";case q:return\"Profiler\";case X:return\"StrictMode\";case H:return\"Suspense\";case Z:return\"SuspenseList\";case J:return\"Activity\"}if(typeof e==\"object\")switch(typeof e.tag==\"number\"&&console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),e.$$typeof){case z:return\"Portal\";case K:return(e.displayName||\"Context\")+\".Provider\";case G:return(e._context.displayName||\"Context\")+\".Consumer\";case B:var r=e.render;return e=e.displayName,e||(e=r.displayName||r.name||\"\",e=e!==\"\"?\"ForwardRef(\"+e+\")\":\"ForwardRef\"),e;case Q:return r=e.displayName||null,r!==null?r:t(e.type)||\"Memo\";case R:r=e._payload,e=e._init;try{return t(e(r))}catch{}}return null}function n(e){return\"\"+e}function c(e){try{n(e);var r=!1}catch{r=!0}if(r){r=console;var a=r.error,s=typeof Symbol==\"function\"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return a.call(r,\"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",s),n(e)}}function b(e){if(e===E)return\"<>\";if(typeof e==\"object\"&&e!==null&&e.$$typeof===R)return\"<...>\";try{var r=t(e);return r?\"<\"+r+\">\":\"<...>\"}catch{return\"<...>\"}}function l(){var e=x.A;return e===null?null:e.getOwner()}function g(){return Error(\"react-stack-top-frame\")}function L(e){if(O.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function W(e,r){function a(){A||(A=!0,console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",r))}a.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:a,configurable:!0})}function M(){var e=t(this.type);return S[e]||(S[e]=!0,console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\")),e=this.props.ref,e!==void 0?e:null}function $(e,r,a,s,u,d,f,v){return a=d.ref,e={$$typeof:w,type:e,key:r,props:d,_owner:u},(a!==void 0?a:null)!==null?Object.defineProperty(e,\"ref\",{enumerable:!1,get:M}):Object.defineProperty(e,\"ref\",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,\"_debugInfo\",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,\"_debugStack\",{configurable:!1,enumerable:!1,writable:!0,value:f}),Object.defineProperty(e,\"_debugTask\",{configurable:!1,enumerable:!1,writable:!0,value:v}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function V(e,r,a,s,u,d,f,v){var i=r.children;if(i!==void 0)if(s)if(re(i)){for(s=0;s<i.length;s++)T(i[s]);Object.freeze&&Object.freeze(i)}else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else T(i);if(O.call(r,\"key\")){i=t(e);var m=Object.keys(r).filter(function(ae){return ae!==\"key\"});s=0<m.length?\"{key: someKey, \"+m.join(\": ..., \")+\": ...}\":\"{key: someKey}\",j[i+s]||(m=0<m.length?\"{\"+m.join(\": ..., \")+\": ...}\":\"{}\",console.error(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,s,i,m,i),j[i+s]=!0)}if(i=null,a!==void 0&&(c(a),i=\"\"+a),L(r)&&(c(r.key),i=\"\"+r.key),\"key\"in r){a={};for(var y in r)y!==\"key\"&&(a[y]=r[y])}else a=r;return i&&W(a,typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e),$(e,i,d,u,l(),a,f,v)}function T(e){typeof e==\"object\"&&e!==null&&e.$$typeof===w&&e._store&&(e._store.validated=1)}var _=U(),w=Symbol.for(\"react.transitional.element\"),z=Symbol.for(\"react.portal\"),E=Symbol.for(\"react.fragment\"),X=Symbol.for(\"react.strict_mode\"),q=Symbol.for(\"react.profiler\");Symbol.for(\"react.provider\");var G=Symbol.for(\"react.consumer\"),K=Symbol.for(\"react.context\"),B=Symbol.for(\"react.forward_ref\"),H=Symbol.for(\"react.suspense\"),Z=Symbol.for(\"react.suspense_list\"),Q=Symbol.for(\"react.memo\"),R=Symbol.for(\"react.lazy\"),J=Symbol.for(\"react.activity\"),ee=Symbol.for(\"react.client.reference\"),x=_.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,O=Object.prototype.hasOwnProperty,re=Array.isArray,k=console.createTask?console.createTask:function(){return null};_={\"react-stack-bottom-frame\":function(e){return e()}};var A,S={},ne=_[\"react-stack-bottom-frame\"].bind(_,g)(),te=k(b(g)),j={};h.Fragment=E,h.jsxDEV=function(e,r,a,s,u,d){var f=1e4>x.recentlyCreatedOwnerStacks++;return V(e,r,a,s,u,d,f?Error(\"react-stack-top-frame\"):ne,f?k(b(e)):te)}})()});var I=N((Ne,Y)=>{\"use strict\";Y.exports=D()});var pe={};ue(pe,{default:()=>_e,frontmatter:()=>me});var o=de(I()),me={title:\"Data Engineering\"};function F(t){let n=Object.assign({p:\"p\",ul:\"ul\",li:\"li\"},t.components);return(0,o.jsxDEV)(o.Fragment,{children:[(0,o.jsxDEV)(n.p,{children:\"We build robust, scalable data infrastructure that powers intelligent applications.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\",lineNumber:5,columnNumber:1},this),`\n`,(0,o.jsxDEV)(n.p,{children:\"Our data engineering solutions ensure your AI systems have access to clean, reliable, and real-time data. We design pipelines that can handle massive scale while maintaining data quality and governance.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\",lineNumber:7,columnNumber:1},this),`\n`,(0,o.jsxDEV)(n.p,{children:\"Our expertise includes:\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\",lineNumber:9,columnNumber:1},this),`\n`,(0,o.jsxDEV)(n.ul,{children:[`\n`,(0,o.jsxDEV)(n.li,{children:\"Real-time data streaming and processing\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\",lineNumber:10,columnNumber:1},this),`\n`,(0,o.jsxDEV)(n.li,{children:\"Data warehouse and lake architecture\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\",lineNumber:11,columnNumber:1},this),`\n`,(0,o.jsxDEV)(n.li,{children:\"ETL/ELT pipeline development\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\",lineNumber:12,columnNumber:1},this),`\n`,(0,o.jsxDEV)(n.li,{children:\"Data quality monitoring and validation\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,o.jsxDEV)(n.li,{children:\"Cloud-native data solutions\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\",lineNumber:14,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\",lineNumber:10,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\",lineNumber:1,columnNumber:1},this)}function be(t={}){let{wrapper:n}=t.components||{};return n?(0,o.jsxDEV)(n,Object.assign({},t,{children:(0,o.jsxDEV)(F,t,void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\"},this):F(t)}var _e=be;return fe(pe);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "services/data-engineering.mdx", "_raw": {"sourceFilePath": "services/data-engineering.mdx", "sourceFileName": "data-engineering.mdx", "sourceFileDir": "services", "contentType": "mdx", "flattenedPath": "services/data-engineering"}, "type": "Page", "slug": "services/data-engineering"}