{"title": "Interactive Demo", "body": {"raw": "\n# AI Agent Platform Demo\n\nExperience the next-generation operating system for AI agents through interactive examples.\n\n<CodePlayground\n  title=\"Create Your First AI Agent\"\n  description=\"Try modifying the agent configuration and see how it affects the execution\"\n  initialCode={`const agent = new Agent({\n  name: 'data-processor',\n  capabilities: ['file-processing', 'api-calls'],\n  task: async (input) => {\n    // Process incoming data\n    const result = await processData(input);\n\n    // Return structured output\n    return {\n      status: 'completed',\n      data: result,\n      timestamp: new Date().toISOString()\n    };\n  }\n});\n\n// Execute the agent\nawait agent.execute({\n  source: 'database',\n  format: 'json'\n});`}\n/>\n\n<Accordion title=\"Agent Architecture Overview\">\n\nOur platform enables AI agents to operate with unprecedented autonomy and coordination:\n\n**Core Components:**\n- **Agent Runtime**: Secure execution environment for AI agents\n- **Communication Layer**: Inter-agent messaging and coordination\n- **Resource Management**: Dynamic allocation of compute and memory\n- **Security Framework**: Sandboxed execution with permission controls\n\n**Key Features:**\n- Real-time agent orchestration\n- Scalable multi-agent workflows  \n- Built-in monitoring and observability\n- Enterprise-grade security and compliance\n\n</Accordion>\n\n<Accordion title=\"Example: Multi-Agent Data Pipeline\">\n\nHere's how multiple AI agents collaborate to process and analyze data:\n\n```typescript\n// Agent 1: Data Ingestion\nconst ingestAgent = new Agent({\n  name: 'data-ingest',\n  capabilities: ['file-processing', 'api-calls'],\n  task: async (input) => {\n    const rawData = await fetchFromSources(input.sources);\n    return { data: rawData, status: 'ingested' };\n  }\n});\n\n// Agent 2: Data Processing  \nconst processAgent = new Agent({\n  name: 'data-processor',\n  capabilities: ['data-transformation', 'validation'],\n  task: async (input) => {\n    const cleanData = await transform(input.data);\n    return { processedData: cleanData, status: 'processed' };\n  }\n});\n\n// Agent 3: Analysis & Insights\nconst analysisAgent = new Agent({\n  name: 'data-analyst',\n  capabilities: ['ml-inference', 'pattern-recognition'],\n  task: async (input) => {\n    const insights = await generateInsights(input.processedData);\n    return { insights, recommendations: insights.recommendations };\n  }\n});\n\n// Orchestration\nconst pipeline = new AgentPipeline([\n  ingestAgent,\n  processAgent, \n  analysisAgent\n]);\n\nawait pipeline.execute({ sources: ['api', 'database', 'files'] });\n```\n\nThis demonstrates how agents can work together autonomously while maintaining clear boundaries and responsibilities.\n\n</Accordion>\n\n<Accordion title=\"Real-World Use Cases\">\n\n**Customer Support Automation**\n- Agent 1: Ticket classification and routing\n- Agent 2: Knowledge base search and response generation  \n- Agent 3: Escalation handling and human handoff\n\n**Financial Analysis Platform**\n- Agent 1: Market data aggregation\n- Agent 2: Risk assessment and modeling\n- Agent 3: Report generation and alerts\n\n**Content Management System**\n- Agent 1: Content ingestion and parsing\n- Agent 2: SEO optimization and metadata generation\n- Agent 3: Publishing and distribution\n\nEach use case demonstrates how our platform enables sophisticated AI workflows that scale with your business needs.\n\n</Accordion>\n\nReady to build your own AI agent platform? Type 'connect' to discuss your specific requirements.\n", "code": "var Component=(()=>{var se=Object.create;var _=Object.defineProperty;var oe=Object.getOwnPropertyDescriptor;var de=Object.getOwnPropertyNames;var le=Object.getPrototypeOf,ce=Object.prototype.hasOwnProperty;var h=(r,n)=>()=>(n||r((n={exports:{}}).exports,n),n.exports),ue=(r,n)=>{for(var s in n)_(r,s,{get:n[s],enumerable:!0})},P=(r,n,s,l)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let c of de(n))!ce.call(r,c)&&c!==s&&_(r,c,{get:()=>n[c],enumerable:!(l=oe(n,c))||l.enumerable});return r};var me=(r,n,s)=>(s=r!=null?se(le(r)):{},P(n||!r||!r.__esModule?_(s,\"default\",{value:r,enumerable:!0}):s,r)),be=r=>P(_({},\"__esModule\",{value:!0}),r);var C=h((ye,j)=>{j.exports=React});var D=h(x=>{\"use strict\";(function(){function r(e){if(e==null)return null;if(typeof e==\"function\")return e.$$typeof===ne?null:e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case g:return\"Fragment\";case q:return\"Profiler\";case K:return\"StrictMode\";case Z:return\"Suspense\";case Q:return\"SuspenseList\";case ee:return\"Activity\"}if(typeof e==\"object\")switch(typeof e.tag==\"number\"&&console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),e.$$typeof){case X:return\"Portal\";case B:return(e.displayName||\"Context\")+\".Provider\";case G:return(e._context.displayName||\"Context\")+\".Consumer\";case H:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||\"\",e=e!==\"\"?\"ForwardRef(\"+e+\")\":\"ForwardRef\"),e;case J:return t=e.displayName||null,t!==null?t:r(e.type)||\"Memo\";case U:t=e._payload,e=e._init;try{return r(e(t))}catch{}}return null}function n(e){return\"\"+e}function s(e){try{n(e);var t=!1}catch{t=!0}if(t){t=console;var a=t.error,d=typeof Symbol==\"function\"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return a.call(t,\"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",d),n(e)}}function l(e){if(e===g)return\"<>\";if(typeof e==\"object\"&&e!==null&&e.$$typeof===U)return\"<...>\";try{var t=r(e);return t?\"<\"+t+\">\":\"<...>\"}catch{return\"<...>\"}}function c(){var e=R.A;return e===null?null:e.getOwner()}function w(){return Error(\"react-stack-top-frame\")}function L(e){if(T.call(e,\"key\")){var t=Object.getOwnPropertyDescriptor(e,\"key\").get;if(t&&t.isReactWarning)return!1}return e.key!==void 0}function W(e,t){function a(){k||(k=!0,console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",t))}a.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:a,configurable:!0})}function $(){var e=r(this.type);return O[e]||(O[e]=!0,console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\")),e=this.props.ref,e!==void 0?e:null}function V(e,t,a,d,u,m,b,N){return a=m.ref,e={$$typeof:E,type:e,key:t,props:m,_owner:u},(a!==void 0?a:null)!==null?Object.defineProperty(e,\"ref\",{enumerable:!1,get:$}):Object.defineProperty(e,\"ref\",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,\"_debugInfo\",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,\"_debugStack\",{configurable:!1,enumerable:!1,writable:!0,value:b}),Object.defineProperty(e,\"_debugTask\",{configurable:!1,enumerable:!1,writable:!0,value:N}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function z(e,t,a,d,u,m,b,N){var o=t.children;if(o!==void 0)if(d)if(ie(o)){for(d=0;d<o.length;d++)A(o[d]);Object.freeze&&Object.freeze(o)}else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else A(o);if(T.call(t,\"key\")){o=r(e);var f=Object.keys(t).filter(function(ae){return ae!==\"key\"});d=0<f.length?\"{key: someKey, \"+f.join(\": ..., \")+\": ...}\":\"{key: someKey}\",S[o+d]||(f=0<f.length?\"{\"+f.join(\": ..., \")+\": ...}\":\"{}\",console.error(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,d,o,f,o),S[o+d]=!0)}if(o=null,a!==void 0&&(s(a),o=\"\"+a),L(t)&&(s(t.key),o=\"\"+t.key),\"key\"in t){a={};for(var y in t)y!==\"key\"&&(a[y]=t[y])}else a=t;return o&&W(a,typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e),V(e,o,m,u,c(),a,b,N)}function A(e){typeof e==\"object\"&&e!==null&&e.$$typeof===E&&e._store&&(e._store.validated=1)}var p=C(),E=Symbol.for(\"react.transitional.element\"),X=Symbol.for(\"react.portal\"),g=Symbol.for(\"react.fragment\"),K=Symbol.for(\"react.strict_mode\"),q=Symbol.for(\"react.profiler\");Symbol.for(\"react.provider\");var G=Symbol.for(\"react.consumer\"),B=Symbol.for(\"react.context\"),H=Symbol.for(\"react.forward_ref\"),Z=Symbol.for(\"react.suspense\"),Q=Symbol.for(\"react.suspense_list\"),J=Symbol.for(\"react.memo\"),U=Symbol.for(\"react.lazy\"),ee=Symbol.for(\"react.activity\"),ne=Symbol.for(\"react.client.reference\"),R=p.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,T=Object.prototype.hasOwnProperty,ie=Array.isArray,v=console.createTask?console.createTask:function(){return null};p={\"react-stack-bottom-frame\":function(e){return e()}};var k,O={},te=p[\"react-stack-bottom-frame\"].bind(p,w)(),re=v(l(w)),S={};x.Fragment=g,x.jsxDEV=function(e,t,a,d,u,m){var b=1e4>R.recentlyCreatedOwnerStacks++;return z(e,t,a,d,u,m,b?Error(\"react-stack-top-frame\"):te,b?v(l(e)):re)}})()});var F=h((xe,I)=>{\"use strict\";I.exports=D()});var ge={};ue(ge,{default:()=>_e,frontmatter:()=>fe});var i=me(F()),fe={title:\"Interactive Demo\"};function Y(r){let n=Object.assign({h1:\"h1\",p:\"p\",strong:\"strong\",ul:\"ul\",li:\"li\",pre:\"pre\",code:\"code\"},r.components),{CodePlayground:s,Accordion:l}=n;return l||M(\"Accordion\",!0,\"35:1-51:13\"),s||M(\"CodePlayground\",!0,\"9:1-33:3\"),(0,i.jsxDEV)(i.Fragment,{children:[(0,i.jsxDEV)(n.h1,{children:\"AI Agent Platform Demo\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:5,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.p,{children:\"Experience the next-generation operating system for AI agents through interactive examples.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:7,columnNumber:1},this),`\n`,(0,i.jsxDEV)(s,{title:\"Create Your First AI Agent\",description:\"Try modifying the agent configuration and see how it affects the execution\",initialCode:`const agent = new Agent({\n  name: 'data-processor',\n  capabilities: ['file-processing', 'api-calls'],\n  task: async (input) => {\n    // Process incoming data\n    const result = await processData(input);\n\n    // Return structured output\n    return {\n      status: 'completed',\n      data: result,\n      timestamp: new Date().toISOString()\n    };\n  }\n});\n\n// Execute the agent\nawait agent.execute({\n  source: 'database',\n  format: 'json'\n});`},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:9,columnNumber:1},this),`\n`,(0,i.jsxDEV)(l,{title:\"Agent Architecture Overview\",children:[(0,i.jsxDEV)(n.p,{children:\"Our platform enables AI agents to operate with unprecedented autonomy and coordination:\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:37,columnNumber:1},this),(0,i.jsxDEV)(n.p,{children:(0,i.jsxDEV)(n.strong,{children:\"Core Components:\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:39,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:39,columnNumber:1},this),(0,i.jsxDEV)(n.ul,{children:[`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Agent Runtime\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:40,columnNumber:3},this),\": Secure execution environment for AI agents\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:40,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Communication Layer\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:41,columnNumber:3},this),\": Inter-agent messaging and coordination\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:41,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Resource Management\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:42,columnNumber:3},this),\": Dynamic allocation of compute and memory\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:42,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Security Framework\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:43,columnNumber:3},this),\": Sandboxed execution with permission controls\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:43,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:40,columnNumber:1},this),(0,i.jsxDEV)(n.p,{children:(0,i.jsxDEV)(n.strong,{children:\"Key Features:\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:45,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:45,columnNumber:1},this),(0,i.jsxDEV)(n.ul,{children:[`\n`,(0,i.jsxDEV)(n.li,{children:\"Real-time agent orchestration\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:46,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:\"Scalable multi-agent workflows\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:47,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:\"Built-in monitoring and observability\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:48,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:\"Enterprise-grade security and compliance\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:49,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:46,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:35,columnNumber:1},this),`\n`,(0,i.jsxDEV)(l,{title:\"Example: Multi-Agent Data Pipeline\",children:[(0,i.jsxDEV)(n.p,{children:\"Here's how multiple AI agents collaborate to process and analyze data:\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:55,columnNumber:1},this),(0,i.jsxDEV)(n.pre,{children:(0,i.jsxDEV)(n.code,{className:\"language-typescript\",children:`// Agent 1: Data Ingestion\nconst ingestAgent = new Agent({\n  name: 'data-ingest',\n  capabilities: ['file-processing', 'api-calls'],\n  task: async (input) => {\n    const rawData = await fetchFromSources(input.sources);\n    return { data: rawData, status: 'ingested' };\n  }\n});\n\n// Agent 2: Data Processing  \nconst processAgent = new Agent({\n  name: 'data-processor',\n  capabilities: ['data-transformation', 'validation'],\n  task: async (input) => {\n    const cleanData = await transform(input.data);\n    return { processedData: cleanData, status: 'processed' };\n  }\n});\n\n// Agent 3: Analysis & Insights\nconst analysisAgent = new Agent({\n  name: 'data-analyst',\n  capabilities: ['ml-inference', 'pattern-recognition'],\n  task: async (input) => {\n    const insights = await generateInsights(input.processedData);\n    return { insights, recommendations: insights.recommendations };\n  }\n});\n\n// Orchestration\nconst pipeline = new AgentPipeline([\n  ingestAgent,\n  processAgent, \n  analysisAgent\n]);\n\nawait pipeline.execute({ sources: ['api', 'database', 'files'] });\n`},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:57,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:57,columnNumber:1},this),(0,i.jsxDEV)(n.p,{children:\"This demonstrates how agents can work together autonomously while maintaining clear boundaries and responsibilities.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:98,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:53,columnNumber:1},this),`\n`,(0,i.jsxDEV)(l,{title:\"Real-World Use Cases\",children:[(0,i.jsxDEV)(n.p,{children:(0,i.jsxDEV)(n.strong,{children:\"Customer Support Automation\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:104,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:104,columnNumber:1},this),(0,i.jsxDEV)(n.ul,{children:[`\n`,(0,i.jsxDEV)(n.li,{children:\"Agent 1: Ticket classification and routing\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:105,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:\"Agent 2: Knowledge base search and response generation\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:106,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:\"Agent 3: Escalation handling and human handoff\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:107,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:105,columnNumber:1},this),(0,i.jsxDEV)(n.p,{children:(0,i.jsxDEV)(n.strong,{children:\"Financial Analysis Platform\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:109,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:109,columnNumber:1},this),(0,i.jsxDEV)(n.ul,{children:[`\n`,(0,i.jsxDEV)(n.li,{children:\"Agent 1: Market data aggregation\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:110,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:\"Agent 2: Risk assessment and modeling\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:111,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:\"Agent 3: Report generation and alerts\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:112,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:110,columnNumber:1},this),(0,i.jsxDEV)(n.p,{children:(0,i.jsxDEV)(n.strong,{children:\"Content Management System\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:114,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:114,columnNumber:1},this),(0,i.jsxDEV)(n.ul,{children:[`\n`,(0,i.jsxDEV)(n.li,{children:\"Agent 1: Content ingestion and parsing\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:115,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:\"Agent 2: SEO optimization and metadata generation\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:116,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:\"Agent 3: Publishing and distribution\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:117,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:115,columnNumber:1},this),(0,i.jsxDEV)(n.p,{children:\"Each use case demonstrates how our platform enables sophisticated AI workflows that scale with your business needs.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:119,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:102,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.p,{children:\"Ready to build your own AI agent platform? Type 'connect' to discuss your specific requirements.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:123,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:1,columnNumber:1},this)}function pe(r={}){let{wrapper:n}=r.components||{};return n?(0,i.jsxDEV)(n,Object.assign({},r,{children:(0,i.jsxDEV)(Y,r,void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\"},this):Y(r)}var _e=pe;function M(r,n,s){throw new Error(\"Expected \"+(n?\"component\":\"object\")+\" `\"+r+\"` to be defined: you likely forgot to import, pass, or provide it.\"+(s?\"\\nIt\\u2019s referenced in your code at `\"+s+\"` in `/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx`\":\"\"))}return be(ge);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "pages/demo.mdx", "_raw": {"sourceFilePath": "pages/demo.mdx", "sourceFileName": "demo.mdx", "sourceFileDir": "pages", "contentType": "mdx", "flattenedPath": "pages/demo"}, "type": "Page", "slug": "pages/demo"}