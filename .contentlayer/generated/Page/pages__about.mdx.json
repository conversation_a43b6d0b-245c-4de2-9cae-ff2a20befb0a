{"title": "About", "body": {"raw": "\nWe're building the next-gen operating system for AI agents.\n\nModern AI will fundamentally change how people use software in their daily lives. Agentic applications could, for the first time, enable computers to help people in much the same way people help each other.\n\nBut it won't happen without new UI patterns, a reimagined privacy model, and a developer platform that makes it possible to build robust consumer experiences and ship radically more capable apps. That's the challenge we're taking on.\n\nWant to know more? Type 'connect' to reach out.\n", "code": "var Component=(()=>{var ae=Object.create;var _=Object.defineProperty;var ie=Object.getOwnPropertyDescriptor;var se=Object.getOwnPropertyNames;var ce=Object.getPrototypeOf,le=Object.prototype.hasOwnProperty;var y=(n,t)=>()=>(t||n((t={exports:{}}).exports,t),t.exports),ue=(n,t)=>{for(var s in t)_(n,s,{get:t[s],enumerable:!0})},P=(n,t,s,m)=>{if(t&&typeof t==\"object\"||typeof t==\"function\")for(let l of se(t))!le.call(n,l)&&l!==s&&_(n,l,{get:()=>t[l],enumerable:!(m=ie(t,l))||m.enumerable});return n};var fe=(n,t,s)=>(s=n!=null?ae(ce(n)):{},P(t||!n||!n.__esModule?_(s,\"default\",{value:n,enumerable:!0}):s,n)),de=n=>P(_({},\"__esModule\",{value:!0}),n);var D=y((he,C)=>{C.exports=React});var I=y(T=>{\"use strict\";(function(){function n(e){if(e==null)return null;if(typeof e==\"function\")return e.$$typeof===ee?null:e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case E:return\"Fragment\";case G:return\"Profiler\";case X:return\"StrictMode\";case H:return\"Suspense\";case Z:return\"SuspenseList\";case J:return\"Activity\"}if(typeof e==\"object\")switch(typeof e.tag==\"number\"&&console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),e.$$typeof){case z:return\"Portal\";case K:return(e.displayName||\"Context\")+\".Provider\";case q:return(e._context.displayName||\"Context\")+\".Consumer\";case B:var r=e.render;return e=e.displayName,e||(e=r.displayName||r.name||\"\",e=e!==\"\"?\"ForwardRef(\"+e+\")\":\"ForwardRef\"),e;case Q:return r=e.displayName||null,r!==null?r:n(e.type)||\"Memo\";case N:r=e._payload,e=e._init;try{return n(e(r))}catch{}}return null}function t(e){return\"\"+e}function s(e){try{t(e);var r=!1}catch{r=!0}if(r){r=console;var o=r.error,i=typeof Symbol==\"function\"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return o.call(r,\"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",i),t(e)}}function m(e){if(e===E)return\"<>\";if(typeof e==\"object\"&&e!==null&&e.$$typeof===N)return\"<...>\";try{var r=n(e);return r?\"<\"+r+\">\":\"<...>\"}catch{return\"<...>\"}}function l(){var e=O.A;return e===null?null:e.getOwner()}function w(){return Error(\"react-stack-top-frame\")}function M(e){if(k.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function W(e,r){function o(){x||(x=!0,console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",r))}o.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:o,configurable:!0})}function L(){var e=n(this.type);return S[e]||(S[e]=!0,console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\")),e=this.props.ref,e!==void 0?e:null}function $(e,r,o,i,u,f,d,h){return o=f.ref,e={$$typeof:v,type:e,key:r,props:f,_owner:u},(o!==void 0?o:null)!==null?Object.defineProperty(e,\"ref\",{enumerable:!1,get:L}):Object.defineProperty(e,\"ref\",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,\"_debugInfo\",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,\"_debugStack\",{configurable:!1,enumerable:!1,writable:!0,value:d}),Object.defineProperty(e,\"_debugTask\",{configurable:!1,enumerable:!1,writable:!0,value:h}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function V(e,r,o,i,u,f,d,h){var a=r.children;if(a!==void 0)if(i)if(re(a)){for(i=0;i<a.length;i++)R(a[i]);Object.freeze&&Object.freeze(a)}else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else R(a);if(k.call(r,\"key\")){a=n(e);var b=Object.keys(r).filter(function(oe){return oe!==\"key\"});i=0<b.length?\"{key: someKey, \"+b.join(\": ..., \")+\": ...}\":\"{key: someKey}\",j[a+i]||(b=0<b.length?\"{\"+b.join(\": ..., \")+\": ...}\":\"{}\",console.error(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,i,a,b,a),j[a+i]=!0)}if(a=null,o!==void 0&&(s(o),a=\"\"+o),M(r)&&(s(r.key),a=\"\"+r.key),\"key\"in r){o={};for(var g in r)g!==\"key\"&&(o[g]=r[g])}else o=r;return a&&W(o,typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e),$(e,a,f,u,l(),o,d,h)}function R(e){typeof e==\"object\"&&e!==null&&e.$$typeof===v&&e._store&&(e._store.validated=1)}var p=D(),v=Symbol.for(\"react.transitional.element\"),z=Symbol.for(\"react.portal\"),E=Symbol.for(\"react.fragment\"),X=Symbol.for(\"react.strict_mode\"),G=Symbol.for(\"react.profiler\");Symbol.for(\"react.provider\");var q=Symbol.for(\"react.consumer\"),K=Symbol.for(\"react.context\"),B=Symbol.for(\"react.forward_ref\"),H=Symbol.for(\"react.suspense\"),Z=Symbol.for(\"react.suspense_list\"),Q=Symbol.for(\"react.memo\"),N=Symbol.for(\"react.lazy\"),J=Symbol.for(\"react.activity\"),ee=Symbol.for(\"react.client.reference\"),O=p.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,k=Object.prototype.hasOwnProperty,re=Array.isArray,A=console.createTask?console.createTask:function(){return null};p={\"react-stack-bottom-frame\":function(e){return e()}};var x,S={},ne=p[\"react-stack-bottom-frame\"].bind(p,w)(),te=A(m(w)),j={};T.Fragment=E,T.jsxDEV=function(e,r,o,i,u,f){var d=1e4>O.recentlyCreatedOwnerStacks++;return V(e,r,o,i,u,f,d?Error(\"react-stack-top-frame\"):ne,d?A(m(e)):te)}})()});var Y=y((ye,U)=>{\"use strict\";U.exports=I()});var _e={};ue(_e,{default:()=>pe,frontmatter:()=>be});var c=fe(Y()),be={title:\"About\"};function F(n){let t=Object.assign({p:\"p\"},n.components);return(0,c.jsxDEV)(c.Fragment,{children:[(0,c.jsxDEV)(t.p,{children:\"We're building the next-gen operating system for AI agents.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-29b89b7b-bfc2-4d7e-a201-902e72f25732.mdx\",lineNumber:5,columnNumber:1},this),`\n`,(0,c.jsxDEV)(t.p,{children:\"Modern AI will fundamentally change how people use software in their daily lives. Agentic applications could, for the first time, enable computers to help people in much the same way people help each other.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-29b89b7b-bfc2-4d7e-a201-902e72f25732.mdx\",lineNumber:7,columnNumber:1},this),`\n`,(0,c.jsxDEV)(t.p,{children:\"But it won't happen without new UI patterns, a reimagined privacy model, and a developer platform that makes it possible to build robust consumer experiences and ship radically more capable apps. That's the challenge we're taking on.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-29b89b7b-bfc2-4d7e-a201-902e72f25732.mdx\",lineNumber:9,columnNumber:1},this),`\n`,(0,c.jsxDEV)(t.p,{children:\"Want to know more? Type 'connect' to reach out.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-29b89b7b-bfc2-4d7e-a201-902e72f25732.mdx\",lineNumber:11,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-29b89b7b-bfc2-4d7e-a201-902e72f25732.mdx\",lineNumber:1,columnNumber:1},this)}function me(n={}){let{wrapper:t}=n.components||{};return t?(0,c.jsxDEV)(t,Object.assign({},n,{children:(0,c.jsxDEV)(F,n,void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-29b89b7b-bfc2-4d7e-a201-902e72f25732.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-29b89b7b-bfc2-4d7e-a201-902e72f25732.mdx\"},this):F(n)}var pe=me;return de(_e);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "pages/about.mdx", "_raw": {"sourceFilePath": "pages/about.mdx", "sourceFileName": "about.mdx", "sourceFileDir": "pages", "contentType": "mdx", "flattenedPath": "pages/about"}, "type": "Page", "slug": "pages/about"}