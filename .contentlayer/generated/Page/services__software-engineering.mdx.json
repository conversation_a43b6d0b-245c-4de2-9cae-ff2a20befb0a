{"title": "Software Engineering", "body": {"raw": "\nWe build scalable, production-ready software systems that power the next generation of AI applications.\n\nOur engineering team combines deep technical expertise with pragmatic problem-solving to deliver robust solutions that scale with your business.\n\n<Accordion title=\"Full-Stack Development\">\n\nWe build complete applications from frontend to backend, with expertise in:\n\n- **Frontend**: React, Next.js, TypeScript, modern CSS frameworks\n- **Backend**: Node.js, Python, Go, microservices architecture  \n- **Databases**: PostgreSQL, MongoDB, Redis, vector databases\n- **Cloud**: AWS, GCP, Azure, serverless architectures\n- **DevOps**: Docker, Kubernetes, CI/CD, monitoring\n\n</Accordion>\n\n<Accordion title=\"AI-Native Architecture\">\n\nWe design systems specifically optimized for AI workloads:\n\n- **Model Serving**: High-performance inference pipelines\n- **Vector Search**: Semantic search and retrieval systems\n- **Real-time Processing**: Stream processing for live AI applications\n- **Scalable Compute**: Auto-scaling infrastructure for variable workloads\n- **Data Pipelines**: ETL/ELT systems optimized for ML workflows\n\n</Accordion>\n\n<Accordion title=\"Enterprise Integration\">\n\nWe help organizations integrate AI capabilities into existing systems:\n\n- **API Development**: RESTful and GraphQL APIs for AI services\n- **Legacy Modernization**: Gradual migration to AI-enhanced systems\n- **Security & Compliance**: Enterprise-grade security and audit trails\n- **Performance Optimization**: System tuning for production workloads\n- **Monitoring & Observability**: Comprehensive logging and metrics\n\n</Accordion>\n\nReady to build something extraordinary? Type 'connect' to discuss your project.\n", "code": "var Component=(()=>{var se=Object.create;var p=Object.defineProperty;var de=Object.getOwnPropertyDescriptor;var ae=Object.getOwnPropertyNames;var oe=Object.getPrototypeOf,ce=Object.prototype.hasOwnProperty;var x=(t,n)=>()=>(n||t((n={exports:{}}).exports,n),n.exports),le=(t,n)=>{for(var d in n)p(t,d,{get:n[d],enumerable:!0})},P=(t,n,d,f)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let c of ae(n))!ce.call(t,c)&&c!==d&&p(t,c,{get:()=>n[c],enumerable:!(f=de(n,c))||f.enumerable});return t};var ue=(t,n,d)=>(d=t!=null?se(oe(t)):{},P(n||!t||!t.__esModule?p(d,\"default\",{value:t,enumerable:!0}):d,t)),be=t=>P(p({},\"__esModule\",{value:!0}),t);var C=x((he,j)=>{j.exports=React});var I=x(v=>{\"use strict\";(function(){function t(e){if(e==null)return null;if(typeof e==\"function\")return e.$$typeof===ee?null:e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case N:return\"Fragment\";case X:return\"Profiler\";case G:return\"StrictMode\";case H:return\"Suspense\";case Q:return\"SuspenseList\";case J:return\"Activity\"}if(typeof e==\"object\")switch(typeof e.tag==\"number\"&&console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),e.$$typeof){case $:return\"Portal\";case q:return(e.displayName||\"Context\")+\".Provider\";case K:return(e._context.displayName||\"Context\")+\".Consumer\";case B:var r=e.render;return e=e.displayName,e||(e=r.displayName||r.name||\"\",e=e!==\"\"?\"ForwardRef(\"+e+\")\":\"ForwardRef\"),e;case Z:return r=e.displayName||null,r!==null?r:t(e.type)||\"Memo\";case T:r=e._payload,e=e._init;try{return t(e(r))}catch{}}return null}function n(e){return\"\"+e}function d(e){try{n(e);var r=!1}catch{r=!0}if(r){r=console;var s=r.error,o=typeof Symbol==\"function\"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return s.call(r,\"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",o),n(e)}}function f(e){if(e===N)return\"<>\";if(typeof e==\"object\"&&e!==null&&e.$$typeof===T)return\"<...>\";try{var r=t(e);return r?\"<\"+r+\">\":\"<...>\"}catch{return\"<...>\"}}function c(){var e=U.A;return e===null?null:e.getOwner()}function w(){return Error(\"react-stack-top-frame\")}function L(e){if(R.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function F(e,r){function s(){A||(A=!0,console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",r))}s.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:s,configurable:!0})}function W(){var e=t(this.type);return k[e]||(k[e]=!0,console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\")),e=this.props.ref,e!==void 0?e:null}function z(e,r,s,o,l,u,b,y){return s=u.ref,e={$$typeof:E,type:e,key:r,props:u,_owner:l},(s!==void 0?s:null)!==null?Object.defineProperty(e,\"ref\",{enumerable:!1,get:W}):Object.defineProperty(e,\"ref\",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,\"_debugInfo\",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,\"_debugStack\",{configurable:!1,enumerable:!1,writable:!0,value:b}),Object.defineProperty(e,\"_debugTask\",{configurable:!1,enumerable:!1,writable:!0,value:y}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function V(e,r,s,o,l,u,b,y){var a=r.children;if(a!==void 0)if(o)if(ne(a)){for(o=0;o<a.length;o++)g(a[o]);Object.freeze&&Object.freeze(a)}else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else g(a);if(R.call(r,\"key\")){a=t(e);var m=Object.keys(r).filter(function(te){return te!==\"key\"});o=0<m.length?\"{key: someKey, \"+m.join(\": ..., \")+\": ...}\":\"{key: someKey}\",O[a+o]||(m=0<m.length?\"{\"+m.join(\": ..., \")+\": ...}\":\"{}\",console.error(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,o,a,m,a),O[a+o]=!0)}if(a=null,s!==void 0&&(d(s),a=\"\"+s),L(r)&&(d(r.key),a=\"\"+r.key),\"key\"in r){s={};for(var h in r)h!==\"key\"&&(s[h]=r[h])}else s=r;return a&&F(s,typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e),z(e,a,u,l,c(),s,b,y)}function g(e){typeof e==\"object\"&&e!==null&&e.$$typeof===E&&e._store&&(e._store.validated=1)}var _=C(),E=Symbol.for(\"react.transitional.element\"),$=Symbol.for(\"react.portal\"),N=Symbol.for(\"react.fragment\"),G=Symbol.for(\"react.strict_mode\"),X=Symbol.for(\"react.profiler\");Symbol.for(\"react.provider\");var K=Symbol.for(\"react.consumer\"),q=Symbol.for(\"react.context\"),B=Symbol.for(\"react.forward_ref\"),H=Symbol.for(\"react.suspense\"),Q=Symbol.for(\"react.suspense_list\"),Z=Symbol.for(\"react.memo\"),T=Symbol.for(\"react.lazy\"),J=Symbol.for(\"react.activity\"),ee=Symbol.for(\"react.client.reference\"),U=_.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,R=Object.prototype.hasOwnProperty,ne=Array.isArray,S=console.createTask?console.createTask:function(){return null};_={\"react-stack-bottom-frame\":function(e){return e()}};var A,k={},ie=_[\"react-stack-bottom-frame\"].bind(_,w)(),re=S(f(w)),O={};v.Fragment=N,v.jsxDEV=function(e,r,s,o,l,u){var b=1e4>U.recentlyCreatedOwnerStacks++;return V(e,r,s,o,l,u,b?Error(\"react-stack-top-frame\"):ie,b?S(f(e)):re)}})()});var M=x((ve,D)=>{\"use strict\";D.exports=I()});var Ne={};le(Ne,{default:()=>_e,frontmatter:()=>me});var i=ue(M()),me={title:\"Software Engineering\"};function Y(t){let n=Object.assign({p:\"p\",ul:\"ul\",li:\"li\",strong:\"strong\"},t.components),{Accordion:d}=n;return d||pe(\"Accordion\",!0,\"9:1-19:13\"),(0,i.jsxDEV)(i.Fragment,{children:[(0,i.jsxDEV)(n.p,{children:\"We build scalable, production-ready software systems that power the next generation of AI applications.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:5,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.p,{children:\"Our engineering team combines deep technical expertise with pragmatic problem-solving to deliver robust solutions that scale with your business.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:7,columnNumber:1},this),`\n`,(0,i.jsxDEV)(d,{title:\"Full-Stack Development\",children:[(0,i.jsxDEV)(n.p,{children:\"We build complete applications from frontend to backend, with expertise in:\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:11,columnNumber:1},this),(0,i.jsxDEV)(n.ul,{children:[`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Frontend\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:13,columnNumber:3},this),\": React, Next.js, TypeScript, modern CSS frameworks\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Backend\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:14,columnNumber:3},this),\": Node.js, Python, Go, microservices architecture\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:14,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Databases\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:15,columnNumber:3},this),\": PostgreSQL, MongoDB, Redis, vector databases\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:15,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Cloud\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:16,columnNumber:3},this),\": AWS, GCP, Azure, serverless architectures\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:16,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"DevOps\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:17,columnNumber:3},this),\": Docker, Kubernetes, CI/CD, monitoring\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:17,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:13,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:9,columnNumber:1},this),`\n`,(0,i.jsxDEV)(d,{title:\"AI-Native Architecture\",children:[(0,i.jsxDEV)(n.p,{children:\"We design systems specifically optimized for AI workloads:\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:23,columnNumber:1},this),(0,i.jsxDEV)(n.ul,{children:[`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Model Serving\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:25,columnNumber:3},this),\": High-performance inference pipelines\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Vector Search\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:26,columnNumber:3},this),\": Semantic search and retrieval systems\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:26,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Real-time Processing\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:27,columnNumber:3},this),\": Stream processing for live AI applications\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:27,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Scalable Compute\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:28,columnNumber:3},this),\": Auto-scaling infrastructure for variable workloads\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:28,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Data Pipelines\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:29,columnNumber:3},this),\": ETL/ELT systems optimized for ML workflows\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:29,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:25,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:21,columnNumber:1},this),`\n`,(0,i.jsxDEV)(d,{title:\"Enterprise Integration\",children:[(0,i.jsxDEV)(n.p,{children:\"We help organizations integrate AI capabilities into existing systems:\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:35,columnNumber:1},this),(0,i.jsxDEV)(n.ul,{children:[`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"API Development\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:37,columnNumber:3},this),\": RESTful and GraphQL APIs for AI services\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:37,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Legacy Modernization\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:38,columnNumber:3},this),\": Gradual migration to AI-enhanced systems\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:38,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Security & Compliance\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:39,columnNumber:3},this),\": Enterprise-grade security and audit trails\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:39,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Performance Optimization\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:40,columnNumber:3},this),\": System tuning for production workloads\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:40,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Monitoring & Observability\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:41,columnNumber:3},this),\": Comprehensive logging and metrics\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:41,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:37,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:33,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.p,{children:\"Ready to build something extraordinary? Type 'connect' to discuss your project.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:45,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:1,columnNumber:1},this)}function fe(t={}){let{wrapper:n}=t.components||{};return n?(0,i.jsxDEV)(n,Object.assign({},t,{children:(0,i.jsxDEV)(Y,t,void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\"},this):Y(t)}var _e=fe;function pe(t,n,d){throw new Error(\"Expected \"+(n?\"component\":\"object\")+\" `\"+t+\"` to be defined: you likely forgot to import, pass, or provide it.\"+(d?\"\\nIt\\u2019s referenced in your code at `\"+d+\"` in `/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx`\":\"\"))}return be(Ne);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "services/software-engineering.mdx", "_raw": {"sourceFilePath": "services/software-engineering.mdx", "sourceFileName": "software-engineering.mdx", "sourceFileDir": "services", "contentType": "mdx", "flattenedPath": "services/software-engineering"}, "type": "Page", "slug": "services/software-engineering"}