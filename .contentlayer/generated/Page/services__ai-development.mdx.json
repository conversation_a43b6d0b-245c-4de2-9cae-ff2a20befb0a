{"title": "AI Development", "body": {"raw": "\nWe build custom AI models and agentic systems that solve real business problems.\n\nOur approach combines cutting-edge research with practical engineering to deliver production-ready AI solutions. From natural language processing to computer vision, we help organizations harness the power of artificial intelligence.\n\nWe specialize in:\n- Custom model development and fine-tuning\n- Agentic workflow design and implementation\n- AI system integration and deployment\n- Performance optimization and scaling", "code": "var Component=(()=>{var oe=Object.create;var p=Object.defineProperty;var ae=Object.getOwnPropertyDescriptor;var se=Object.getOwnPropertyNames;var ce=Object.getPrototypeOf,le=Object.prototype.hasOwnProperty;var g=(t,n)=>()=>(n||t((n={exports:{}}).exports,n),n.exports),ue=(t,n)=>{for(var c in n)p(t,c,{get:n[c],enumerable:!0})},P=(t,n,c,m)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let l of se(n))!le.call(t,l)&&l!==c&&p(t,l,{get:()=>n[l],enumerable:!(m=ae(n,l))||m.enumerable});return t};var fe=(t,n,c)=>(c=t!=null?oe(ce(t)):{},P(n||!t||!t.__esModule?p(c,\"default\",{value:t,enumerable:!0}):c,t)),de=t=>P(p({},\"__esModule\",{value:!0}),t);var U=g((ve,C)=>{C.exports=React});var D=g(N=>{\"use strict\";(function(){function t(e){if(e==null)return null;if(typeof e==\"function\")return e.$$typeof===ee?null:e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case E:return\"Fragment\";case G:return\"Profiler\";case X:return\"StrictMode\";case H:return\"Suspense\";case Z:return\"SuspenseList\";case J:return\"Activity\"}if(typeof e==\"object\")switch(typeof e.tag==\"number\"&&console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),e.$$typeof){case V:return\"Portal\";case K:return(e.displayName||\"Context\")+\".Provider\";case q:return(e._context.displayName||\"Context\")+\".Consumer\";case B:var r=e.render;return e=e.displayName,e||(e=r.displayName||r.name||\"\",e=e!==\"\"?\"ForwardRef(\"+e+\")\":\"ForwardRef\"),e;case Q:return r=e.displayName||null,r!==null?r:t(e.type)||\"Memo\";case R:r=e._payload,e=e._init;try{return t(e(r))}catch{}}return null}function n(e){return\"\"+e}function c(e){try{n(e);var r=!1}catch{r=!0}if(r){r=console;var i=r.error,a=typeof Symbol==\"function\"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return i.call(r,\"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",a),n(e)}}function m(e){if(e===E)return\"<>\";if(typeof e==\"object\"&&e!==null&&e.$$typeof===R)return\"<...>\";try{var r=t(e);return r?\"<\"+r+\">\":\"<...>\"}catch{return\"<...>\"}}function l(){var e=x.A;return e===null?null:e.getOwner()}function h(){return Error(\"react-stack-top-frame\")}function W(e){if(O.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function M(e,r){function i(){k||(k=!0,console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",r))}i.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:i,configurable:!0})}function L(){var e=t(this.type);return S[e]||(S[e]=!0,console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\")),e=this.props.ref,e!==void 0?e:null}function $(e,r,i,a,u,f,d,v){return i=f.ref,e={$$typeof:w,type:e,key:r,props:f,_owner:u},(i!==void 0?i:null)!==null?Object.defineProperty(e,\"ref\",{enumerable:!1,get:L}):Object.defineProperty(e,\"ref\",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,\"_debugInfo\",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,\"_debugStack\",{configurable:!1,enumerable:!1,writable:!0,value:d}),Object.defineProperty(e,\"_debugTask\",{configurable:!1,enumerable:!1,writable:!0,value:v}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function z(e,r,i,a,u,f,d,v){var o=r.children;if(o!==void 0)if(a)if(re(o)){for(a=0;a<o.length;a++)T(o[a]);Object.freeze&&Object.freeze(o)}else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else T(o);if(O.call(r,\"key\")){o=t(e);var b=Object.keys(r).filter(function(ie){return ie!==\"key\"});a=0<b.length?\"{key: someKey, \"+b.join(\": ..., \")+\": ...}\":\"{key: someKey}\",j[o+a]||(b=0<b.length?\"{\"+b.join(\": ..., \")+\": ...}\":\"{}\",console.error(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,a,o,b,o),j[o+a]=!0)}if(o=null,i!==void 0&&(c(i),o=\"\"+i),W(r)&&(c(r.key),o=\"\"+r.key),\"key\"in r){i={};for(var y in r)y!==\"key\"&&(i[y]=r[y])}else i=r;return o&&M(i,typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e),$(e,o,f,u,l(),i,d,v)}function T(e){typeof e==\"object\"&&e!==null&&e.$$typeof===w&&e._store&&(e._store.validated=1)}var _=U(),w=Symbol.for(\"react.transitional.element\"),V=Symbol.for(\"react.portal\"),E=Symbol.for(\"react.fragment\"),X=Symbol.for(\"react.strict_mode\"),G=Symbol.for(\"react.profiler\");Symbol.for(\"react.provider\");var q=Symbol.for(\"react.consumer\"),K=Symbol.for(\"react.context\"),B=Symbol.for(\"react.forward_ref\"),H=Symbol.for(\"react.suspense\"),Z=Symbol.for(\"react.suspense_list\"),Q=Symbol.for(\"react.memo\"),R=Symbol.for(\"react.lazy\"),J=Symbol.for(\"react.activity\"),ee=Symbol.for(\"react.client.reference\"),x=_.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,O=Object.prototype.hasOwnProperty,re=Array.isArray,A=console.createTask?console.createTask:function(){return null};_={\"react-stack-bottom-frame\":function(e){return e()}};var k,S={},ne=_[\"react-stack-bottom-frame\"].bind(_,h)(),te=A(m(h)),j={};N.Fragment=E,N.jsxDEV=function(e,r,i,a,u,f){var d=1e4>x.recentlyCreatedOwnerStacks++;return z(e,r,i,a,u,f,d?Error(\"react-stack-top-frame\"):ne,d?A(m(e)):te)}})()});var Y=g((ge,I)=>{\"use strict\";I.exports=D()});var pe={};ue(pe,{default:()=>_e,frontmatter:()=>be});var s=fe(Y()),be={title:\"AI Development\"};function F(t){let n=Object.assign({p:\"p\",ul:\"ul\",li:\"li\"},t.components);return(0,s.jsxDEV)(s.Fragment,{children:[(0,s.jsxDEV)(n.p,{children:\"We build custom AI models and agentic systems that solve real business problems.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\",lineNumber:5,columnNumber:1},this),`\n`,(0,s.jsxDEV)(n.p,{children:\"Our approach combines cutting-edge research with practical engineering to deliver production-ready AI solutions. From natural language processing to computer vision, we help organizations harness the power of artificial intelligence.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\",lineNumber:7,columnNumber:1},this),`\n`,(0,s.jsxDEV)(n.p,{children:\"We specialize in:\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\",lineNumber:9,columnNumber:1},this),`\n`,(0,s.jsxDEV)(n.ul,{children:[`\n`,(0,s.jsxDEV)(n.li,{children:\"Custom model development and fine-tuning\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\",lineNumber:10,columnNumber:1},this),`\n`,(0,s.jsxDEV)(n.li,{children:\"Agentic workflow design and implementation\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\",lineNumber:11,columnNumber:1},this),`\n`,(0,s.jsxDEV)(n.li,{children:\"AI system integration and deployment\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\",lineNumber:12,columnNumber:1},this),`\n`,(0,s.jsxDEV)(n.li,{children:\"Performance optimization and scaling\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\",lineNumber:13,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\",lineNumber:10,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\",lineNumber:1,columnNumber:1},this)}function me(t={}){let{wrapper:n}=t.components||{};return n?(0,s.jsxDEV)(n,Object.assign({},t,{children:(0,s.jsxDEV)(F,t,void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\"},this):F(t)}var _e=me;return de(pe);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "services/ai-development.mdx", "_raw": {"sourceFilePath": "services/ai-development.mdx", "sourceFileName": "ai-development.mdx", "sourceFileDir": "services", "contentType": "mdx", "flattenedPath": "services/ai-development"}, "type": "Page", "slug": "services/ai-development"}