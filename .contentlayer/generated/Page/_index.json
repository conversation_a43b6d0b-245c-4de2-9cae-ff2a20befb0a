[{"title": "Bokoo Acquisition", "body": {"raw": "EspirAI Labs has successfully completed the acquisition of Bokoo, a leader in high-quality data labeling and annotation.\n\nThis strategic move unlocks true end-to-end data capabilities for our clients, from raw data curation and human-in-the-loop evaluation to the deployment of production-grade AI systems.\n", "code": "var Component=(()=>{var d=Object.create;var s=Object.defineProperty;var m=Object.getOwnPropertyDescriptor;var p=Object.getOwnPropertyNames;var h=Object.getPrototypeOf,f=Object.prototype.hasOwnProperty;var x=(t,n)=>()=>(n||t((n={exports:{}}).exports,n),n.exports),g=(t,n)=>{for(var o in n)s(t,o,{get:n[o],enumerable:!0})},r=(t,n,o,i)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let a of p(n))!f.call(t,a)&&a!==o&&s(t,a,{get:()=>n[a],enumerable:!(i=m(n,a))||i.enumerable});return t};var j=(t,n,o)=>(o=t!=null?d(h(t)):{},r(n||!t||!t.__esModule?s(o,\"default\",{value:t,enumerable:!0}):o,t)),_=t=>r(s({},\"__esModule\",{value:!0}),t);var l=x((M,c)=>{c.exports=_jsx_runtime});var q={};g(q,{default:()=>k,frontmatter:()=>b});var e=j(l()),b={title:\"Bokoo Acquisition\"};function u(t){let n=Object.assign({p:\"p\"},t.components);return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(n.p,{children:\"EspirAI Labs has successfully completed the acquisition of Bokoo, a leader in high-quality data labeling and annotation.\"}),`\n`,(0,e.jsx)(n.p,{children:\"This strategic move unlocks true end-to-end data capabilities for our clients, from raw data curation and human-in-the-loop evaluation to the deployment of production-grade AI systems.\"})]})}function y(t={}){let{wrapper:n}=t.components||{};return n?(0,e.jsx)(n,Object.assign({},t,{children:(0,e.jsx)(u,t)})):u(t)}var k=y;return _(q);})();\n;return Component;"}, "_id": "news/bokoo-acquisition.mdx", "_raw": {"sourceFilePath": "news/bokoo-acquisition.mdx", "sourceFileName": "bokoo-acquisition.mdx", "sourceFileDir": "news", "contentType": "mdx", "flattenedPath": "news/bokoo-acquisition"}, "type": "Page", "slug": "news/bokoo-acquisition"}, {"title": "AI Development", "body": {"raw": "\nWe build custom AI models and agentic systems that solve real business problems.\n\nOur approach combines cutting-edge research with practical engineering to deliver production-ready AI solutions. From natural language processing to computer vision, we help organizations harness the power of artificial intelligence.\n\nWe specialize in:\n- Custom model development and fine-tuning\n- Agentic workflow design and implementation\n- AI system integration and deployment\n- Performance optimization and scaling", "code": "var Component=(()=>{var oe=Object.create;var p=Object.defineProperty;var ae=Object.getOwnPropertyDescriptor;var se=Object.getOwnPropertyNames;var ce=Object.getPrototypeOf,le=Object.prototype.hasOwnProperty;var g=(t,n)=>()=>(n||t((n={exports:{}}).exports,n),n.exports),ue=(t,n)=>{for(var c in n)p(t,c,{get:n[c],enumerable:!0})},P=(t,n,c,m)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let l of se(n))!le.call(t,l)&&l!==c&&p(t,l,{get:()=>n[l],enumerable:!(m=ae(n,l))||m.enumerable});return t};var fe=(t,n,c)=>(c=t!=null?oe(ce(t)):{},P(n||!t||!t.__esModule?p(c,\"default\",{value:t,enumerable:!0}):c,t)),de=t=>P(p({},\"__esModule\",{value:!0}),t);var U=g((ve,C)=>{C.exports=React});var D=g(N=>{\"use strict\";(function(){function t(e){if(e==null)return null;if(typeof e==\"function\")return e.$$typeof===ee?null:e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case E:return\"Fragment\";case G:return\"Profiler\";case X:return\"StrictMode\";case H:return\"Suspense\";case Z:return\"SuspenseList\";case J:return\"Activity\"}if(typeof e==\"object\")switch(typeof e.tag==\"number\"&&console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),e.$$typeof){case V:return\"Portal\";case K:return(e.displayName||\"Context\")+\".Provider\";case q:return(e._context.displayName||\"Context\")+\".Consumer\";case B:var r=e.render;return e=e.displayName,e||(e=r.displayName||r.name||\"\",e=e!==\"\"?\"ForwardRef(\"+e+\")\":\"ForwardRef\"),e;case Q:return r=e.displayName||null,r!==null?r:t(e.type)||\"Memo\";case R:r=e._payload,e=e._init;try{return t(e(r))}catch{}}return null}function n(e){return\"\"+e}function c(e){try{n(e);var r=!1}catch{r=!0}if(r){r=console;var i=r.error,a=typeof Symbol==\"function\"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return i.call(r,\"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",a),n(e)}}function m(e){if(e===E)return\"<>\";if(typeof e==\"object\"&&e!==null&&e.$$typeof===R)return\"<...>\";try{var r=t(e);return r?\"<\"+r+\">\":\"<...>\"}catch{return\"<...>\"}}function l(){var e=x.A;return e===null?null:e.getOwner()}function h(){return Error(\"react-stack-top-frame\")}function W(e){if(O.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function M(e,r){function i(){k||(k=!0,console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",r))}i.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:i,configurable:!0})}function L(){var e=t(this.type);return S[e]||(S[e]=!0,console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\")),e=this.props.ref,e!==void 0?e:null}function $(e,r,i,a,u,f,d,v){return i=f.ref,e={$$typeof:w,type:e,key:r,props:f,_owner:u},(i!==void 0?i:null)!==null?Object.defineProperty(e,\"ref\",{enumerable:!1,get:L}):Object.defineProperty(e,\"ref\",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,\"_debugInfo\",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,\"_debugStack\",{configurable:!1,enumerable:!1,writable:!0,value:d}),Object.defineProperty(e,\"_debugTask\",{configurable:!1,enumerable:!1,writable:!0,value:v}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function z(e,r,i,a,u,f,d,v){var o=r.children;if(o!==void 0)if(a)if(re(o)){for(a=0;a<o.length;a++)T(o[a]);Object.freeze&&Object.freeze(o)}else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else T(o);if(O.call(r,\"key\")){o=t(e);var b=Object.keys(r).filter(function(ie){return ie!==\"key\"});a=0<b.length?\"{key: someKey, \"+b.join(\": ..., \")+\": ...}\":\"{key: someKey}\",j[o+a]||(b=0<b.length?\"{\"+b.join(\": ..., \")+\": ...}\":\"{}\",console.error(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,a,o,b,o),j[o+a]=!0)}if(o=null,i!==void 0&&(c(i),o=\"\"+i),W(r)&&(c(r.key),o=\"\"+r.key),\"key\"in r){i={};for(var y in r)y!==\"key\"&&(i[y]=r[y])}else i=r;return o&&M(i,typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e),$(e,o,f,u,l(),i,d,v)}function T(e){typeof e==\"object\"&&e!==null&&e.$$typeof===w&&e._store&&(e._store.validated=1)}var _=U(),w=Symbol.for(\"react.transitional.element\"),V=Symbol.for(\"react.portal\"),E=Symbol.for(\"react.fragment\"),X=Symbol.for(\"react.strict_mode\"),G=Symbol.for(\"react.profiler\");Symbol.for(\"react.provider\");var q=Symbol.for(\"react.consumer\"),K=Symbol.for(\"react.context\"),B=Symbol.for(\"react.forward_ref\"),H=Symbol.for(\"react.suspense\"),Z=Symbol.for(\"react.suspense_list\"),Q=Symbol.for(\"react.memo\"),R=Symbol.for(\"react.lazy\"),J=Symbol.for(\"react.activity\"),ee=Symbol.for(\"react.client.reference\"),x=_.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,O=Object.prototype.hasOwnProperty,re=Array.isArray,A=console.createTask?console.createTask:function(){return null};_={\"react-stack-bottom-frame\":function(e){return e()}};var k,S={},ne=_[\"react-stack-bottom-frame\"].bind(_,h)(),te=A(m(h)),j={};N.Fragment=E,N.jsxDEV=function(e,r,i,a,u,f){var d=1e4>x.recentlyCreatedOwnerStacks++;return z(e,r,i,a,u,f,d?Error(\"react-stack-top-frame\"):ne,d?A(m(e)):te)}})()});var Y=g((ge,I)=>{\"use strict\";I.exports=D()});var pe={};ue(pe,{default:()=>_e,frontmatter:()=>be});var s=fe(Y()),be={title:\"AI Development\"};function F(t){let n=Object.assign({p:\"p\",ul:\"ul\",li:\"li\"},t.components);return(0,s.jsxDEV)(s.Fragment,{children:[(0,s.jsxDEV)(n.p,{children:\"We build custom AI models and agentic systems that solve real business problems.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\",lineNumber:5,columnNumber:1},this),`\n`,(0,s.jsxDEV)(n.p,{children:\"Our approach combines cutting-edge research with practical engineering to deliver production-ready AI solutions. From natural language processing to computer vision, we help organizations harness the power of artificial intelligence.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\",lineNumber:7,columnNumber:1},this),`\n`,(0,s.jsxDEV)(n.p,{children:\"We specialize in:\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\",lineNumber:9,columnNumber:1},this),`\n`,(0,s.jsxDEV)(n.ul,{children:[`\n`,(0,s.jsxDEV)(n.li,{children:\"Custom model development and fine-tuning\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\",lineNumber:10,columnNumber:1},this),`\n`,(0,s.jsxDEV)(n.li,{children:\"Agentic workflow design and implementation\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\",lineNumber:11,columnNumber:1},this),`\n`,(0,s.jsxDEV)(n.li,{children:\"AI system integration and deployment\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\",lineNumber:12,columnNumber:1},this),`\n`,(0,s.jsxDEV)(n.li,{children:\"Performance optimization and scaling\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\",lineNumber:13,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\",lineNumber:10,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\",lineNumber:1,columnNumber:1},this)}function me(t={}){let{wrapper:n}=t.components||{};return n?(0,s.jsxDEV)(n,Object.assign({},t,{children:(0,s.jsxDEV)(F,t,void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-0e3848cb-6bca-4b17-98e6-ff8b448f4eff.mdx\"},this):F(t)}var _e=me;return de(pe);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "services/ai-development.mdx", "_raw": {"sourceFilePath": "services/ai-development.mdx", "sourceFileName": "ai-development.mdx", "sourceFileDir": "services", "contentType": "mdx", "flattenedPath": "services/ai-development"}, "type": "Page", "slug": "services/ai-development"}, {"title": "Data Engineering", "body": {"raw": "\nWe build robust, scalable data infrastructure that powers intelligent applications.\n\nOur data engineering solutions ensure your AI systems have access to clean, reliable, and real-time data. We design pipelines that can handle massive scale while maintaining data quality and governance.\n\nOur expertise includes:\n- Real-time data streaming and processing\n- Data warehouse and lake architecture\n- ETL/ELT pipeline development\n- Data quality monitoring and validation\n- Cloud-native data solutions", "code": "var Component=(()=>{var ie=Object.create;var p=Object.defineProperty;var oe=Object.getOwnPropertyDescriptor;var se=Object.getOwnPropertyNames;var ce=Object.getPrototypeOf,le=Object.prototype.hasOwnProperty;var N=(t,n)=>()=>(n||t((n={exports:{}}).exports,n),n.exports),ue=(t,n)=>{for(var c in n)p(t,c,{get:n[c],enumerable:!0})},P=(t,n,c,b)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let l of se(n))!le.call(t,l)&&l!==c&&p(t,l,{get:()=>n[l],enumerable:!(b=oe(n,l))||b.enumerable});return t};var de=(t,n,c)=>(c=t!=null?ie(ce(t)):{},P(n||!t||!t.__esModule?p(c,\"default\",{value:t,enumerable:!0}):c,t)),fe=t=>P(p({},\"__esModule\",{value:!0}),t);var U=N((ve,C)=>{C.exports=React});var D=N(h=>{\"use strict\";(function(){function t(e){if(e==null)return null;if(typeof e==\"function\")return e.$$typeof===ee?null:e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case E:return\"Fragment\";case q:return\"Profiler\";case X:return\"StrictMode\";case H:return\"Suspense\";case Z:return\"SuspenseList\";case J:return\"Activity\"}if(typeof e==\"object\")switch(typeof e.tag==\"number\"&&console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),e.$$typeof){case z:return\"Portal\";case K:return(e.displayName||\"Context\")+\".Provider\";case G:return(e._context.displayName||\"Context\")+\".Consumer\";case B:var r=e.render;return e=e.displayName,e||(e=r.displayName||r.name||\"\",e=e!==\"\"?\"ForwardRef(\"+e+\")\":\"ForwardRef\"),e;case Q:return r=e.displayName||null,r!==null?r:t(e.type)||\"Memo\";case R:r=e._payload,e=e._init;try{return t(e(r))}catch{}}return null}function n(e){return\"\"+e}function c(e){try{n(e);var r=!1}catch{r=!0}if(r){r=console;var a=r.error,s=typeof Symbol==\"function\"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return a.call(r,\"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",s),n(e)}}function b(e){if(e===E)return\"<>\";if(typeof e==\"object\"&&e!==null&&e.$$typeof===R)return\"<...>\";try{var r=t(e);return r?\"<\"+r+\">\":\"<...>\"}catch{return\"<...>\"}}function l(){var e=x.A;return e===null?null:e.getOwner()}function g(){return Error(\"react-stack-top-frame\")}function L(e){if(O.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function W(e,r){function a(){A||(A=!0,console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",r))}a.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:a,configurable:!0})}function M(){var e=t(this.type);return S[e]||(S[e]=!0,console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\")),e=this.props.ref,e!==void 0?e:null}function $(e,r,a,s,u,d,f,v){return a=d.ref,e={$$typeof:w,type:e,key:r,props:d,_owner:u},(a!==void 0?a:null)!==null?Object.defineProperty(e,\"ref\",{enumerable:!1,get:M}):Object.defineProperty(e,\"ref\",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,\"_debugInfo\",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,\"_debugStack\",{configurable:!1,enumerable:!1,writable:!0,value:f}),Object.defineProperty(e,\"_debugTask\",{configurable:!1,enumerable:!1,writable:!0,value:v}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function V(e,r,a,s,u,d,f,v){var i=r.children;if(i!==void 0)if(s)if(re(i)){for(s=0;s<i.length;s++)T(i[s]);Object.freeze&&Object.freeze(i)}else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else T(i);if(O.call(r,\"key\")){i=t(e);var m=Object.keys(r).filter(function(ae){return ae!==\"key\"});s=0<m.length?\"{key: someKey, \"+m.join(\": ..., \")+\": ...}\":\"{key: someKey}\",j[i+s]||(m=0<m.length?\"{\"+m.join(\": ..., \")+\": ...}\":\"{}\",console.error(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,s,i,m,i),j[i+s]=!0)}if(i=null,a!==void 0&&(c(a),i=\"\"+a),L(r)&&(c(r.key),i=\"\"+r.key),\"key\"in r){a={};for(var y in r)y!==\"key\"&&(a[y]=r[y])}else a=r;return i&&W(a,typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e),$(e,i,d,u,l(),a,f,v)}function T(e){typeof e==\"object\"&&e!==null&&e.$$typeof===w&&e._store&&(e._store.validated=1)}var _=U(),w=Symbol.for(\"react.transitional.element\"),z=Symbol.for(\"react.portal\"),E=Symbol.for(\"react.fragment\"),X=Symbol.for(\"react.strict_mode\"),q=Symbol.for(\"react.profiler\");Symbol.for(\"react.provider\");var G=Symbol.for(\"react.consumer\"),K=Symbol.for(\"react.context\"),B=Symbol.for(\"react.forward_ref\"),H=Symbol.for(\"react.suspense\"),Z=Symbol.for(\"react.suspense_list\"),Q=Symbol.for(\"react.memo\"),R=Symbol.for(\"react.lazy\"),J=Symbol.for(\"react.activity\"),ee=Symbol.for(\"react.client.reference\"),x=_.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,O=Object.prototype.hasOwnProperty,re=Array.isArray,k=console.createTask?console.createTask:function(){return null};_={\"react-stack-bottom-frame\":function(e){return e()}};var A,S={},ne=_[\"react-stack-bottom-frame\"].bind(_,g)(),te=k(b(g)),j={};h.Fragment=E,h.jsxDEV=function(e,r,a,s,u,d){var f=1e4>x.recentlyCreatedOwnerStacks++;return V(e,r,a,s,u,d,f?Error(\"react-stack-top-frame\"):ne,f?k(b(e)):te)}})()});var I=N((Ne,Y)=>{\"use strict\";Y.exports=D()});var pe={};ue(pe,{default:()=>_e,frontmatter:()=>me});var o=de(I()),me={title:\"Data Engineering\"};function F(t){let n=Object.assign({p:\"p\",ul:\"ul\",li:\"li\"},t.components);return(0,o.jsxDEV)(o.Fragment,{children:[(0,o.jsxDEV)(n.p,{children:\"We build robust, scalable data infrastructure that powers intelligent applications.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\",lineNumber:5,columnNumber:1},this),`\n`,(0,o.jsxDEV)(n.p,{children:\"Our data engineering solutions ensure your AI systems have access to clean, reliable, and real-time data. We design pipelines that can handle massive scale while maintaining data quality and governance.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\",lineNumber:7,columnNumber:1},this),`\n`,(0,o.jsxDEV)(n.p,{children:\"Our expertise includes:\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\",lineNumber:9,columnNumber:1},this),`\n`,(0,o.jsxDEV)(n.ul,{children:[`\n`,(0,o.jsxDEV)(n.li,{children:\"Real-time data streaming and processing\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\",lineNumber:10,columnNumber:1},this),`\n`,(0,o.jsxDEV)(n.li,{children:\"Data warehouse and lake architecture\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\",lineNumber:11,columnNumber:1},this),`\n`,(0,o.jsxDEV)(n.li,{children:\"ETL/ELT pipeline development\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\",lineNumber:12,columnNumber:1},this),`\n`,(0,o.jsxDEV)(n.li,{children:\"Data quality monitoring and validation\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,o.jsxDEV)(n.li,{children:\"Cloud-native data solutions\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\",lineNumber:14,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\",lineNumber:10,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\",lineNumber:1,columnNumber:1},this)}function be(t={}){let{wrapper:n}=t.components||{};return n?(0,o.jsxDEV)(n,Object.assign({},t,{children:(0,o.jsxDEV)(F,t,void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-8c638e44-ca56-47bb-8c11-f38adf5929a6.mdx\"},this):F(t)}var _e=be;return fe(pe);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "services/data-engineering.mdx", "_raw": {"sourceFilePath": "services/data-engineering.mdx", "sourceFileName": "data-engineering.mdx", "sourceFileDir": "services", "contentType": "mdx", "flattenedPath": "services/data-engineering"}, "type": "Page", "slug": "services/data-engineering"}, {"title": "Software Engineering", "body": {"raw": "\nWe build scalable, production-ready software systems that power the next generation of AI applications.\n\nOur engineering team combines deep technical expertise with pragmatic problem-solving to deliver robust solutions that scale with your business.\n\n<Accordion title=\"Full-Stack Development\">\n\nWe build complete applications from frontend to backend, with expertise in:\n\n- **Frontend**: React, Next.js, TypeScript, modern CSS frameworks\n- **Backend**: Node.js, Python, Go, microservices architecture  \n- **Databases**: PostgreSQL, MongoDB, Redis, vector databases\n- **Cloud**: AWS, GCP, Azure, serverless architectures\n- **DevOps**: Docker, Kubernetes, CI/CD, monitoring\n\n</Accordion>\n\n<Accordion title=\"AI-Native Architecture\">\n\nWe design systems specifically optimized for AI workloads:\n\n- **Model Serving**: High-performance inference pipelines\n- **Vector Search**: Semantic search and retrieval systems\n- **Real-time Processing**: Stream processing for live AI applications\n- **Scalable Compute**: Auto-scaling infrastructure for variable workloads\n- **Data Pipelines**: ETL/ELT systems optimized for ML workflows\n\n</Accordion>\n\n<Accordion title=\"Enterprise Integration\">\n\nWe help organizations integrate AI capabilities into existing systems:\n\n- **API Development**: RESTful and GraphQL APIs for AI services\n- **Legacy Modernization**: Gradual migration to AI-enhanced systems\n- **Security & Compliance**: Enterprise-grade security and audit trails\n- **Performance Optimization**: System tuning for production workloads\n- **Monitoring & Observability**: Comprehensive logging and metrics\n\n</Accordion>\n\nReady to build something extraordinary? Type 'connect' to discuss your project.\n", "code": "var Component=(()=>{var se=Object.create;var p=Object.defineProperty;var de=Object.getOwnPropertyDescriptor;var ae=Object.getOwnPropertyNames;var oe=Object.getPrototypeOf,ce=Object.prototype.hasOwnProperty;var x=(t,n)=>()=>(n||t((n={exports:{}}).exports,n),n.exports),le=(t,n)=>{for(var d in n)p(t,d,{get:n[d],enumerable:!0})},P=(t,n,d,f)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let c of ae(n))!ce.call(t,c)&&c!==d&&p(t,c,{get:()=>n[c],enumerable:!(f=de(n,c))||f.enumerable});return t};var ue=(t,n,d)=>(d=t!=null?se(oe(t)):{},P(n||!t||!t.__esModule?p(d,\"default\",{value:t,enumerable:!0}):d,t)),be=t=>P(p({},\"__esModule\",{value:!0}),t);var C=x((he,j)=>{j.exports=React});var I=x(v=>{\"use strict\";(function(){function t(e){if(e==null)return null;if(typeof e==\"function\")return e.$$typeof===ee?null:e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case N:return\"Fragment\";case X:return\"Profiler\";case G:return\"StrictMode\";case H:return\"Suspense\";case Q:return\"SuspenseList\";case J:return\"Activity\"}if(typeof e==\"object\")switch(typeof e.tag==\"number\"&&console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),e.$$typeof){case $:return\"Portal\";case q:return(e.displayName||\"Context\")+\".Provider\";case K:return(e._context.displayName||\"Context\")+\".Consumer\";case B:var r=e.render;return e=e.displayName,e||(e=r.displayName||r.name||\"\",e=e!==\"\"?\"ForwardRef(\"+e+\")\":\"ForwardRef\"),e;case Z:return r=e.displayName||null,r!==null?r:t(e.type)||\"Memo\";case T:r=e._payload,e=e._init;try{return t(e(r))}catch{}}return null}function n(e){return\"\"+e}function d(e){try{n(e);var r=!1}catch{r=!0}if(r){r=console;var s=r.error,o=typeof Symbol==\"function\"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return s.call(r,\"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",o),n(e)}}function f(e){if(e===N)return\"<>\";if(typeof e==\"object\"&&e!==null&&e.$$typeof===T)return\"<...>\";try{var r=t(e);return r?\"<\"+r+\">\":\"<...>\"}catch{return\"<...>\"}}function c(){var e=U.A;return e===null?null:e.getOwner()}function w(){return Error(\"react-stack-top-frame\")}function L(e){if(R.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function F(e,r){function s(){A||(A=!0,console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",r))}s.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:s,configurable:!0})}function W(){var e=t(this.type);return k[e]||(k[e]=!0,console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\")),e=this.props.ref,e!==void 0?e:null}function z(e,r,s,o,l,u,b,y){return s=u.ref,e={$$typeof:E,type:e,key:r,props:u,_owner:l},(s!==void 0?s:null)!==null?Object.defineProperty(e,\"ref\",{enumerable:!1,get:W}):Object.defineProperty(e,\"ref\",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,\"_debugInfo\",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,\"_debugStack\",{configurable:!1,enumerable:!1,writable:!0,value:b}),Object.defineProperty(e,\"_debugTask\",{configurable:!1,enumerable:!1,writable:!0,value:y}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function V(e,r,s,o,l,u,b,y){var a=r.children;if(a!==void 0)if(o)if(ne(a)){for(o=0;o<a.length;o++)g(a[o]);Object.freeze&&Object.freeze(a)}else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else g(a);if(R.call(r,\"key\")){a=t(e);var m=Object.keys(r).filter(function(te){return te!==\"key\"});o=0<m.length?\"{key: someKey, \"+m.join(\": ..., \")+\": ...}\":\"{key: someKey}\",O[a+o]||(m=0<m.length?\"{\"+m.join(\": ..., \")+\": ...}\":\"{}\",console.error(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,o,a,m,a),O[a+o]=!0)}if(a=null,s!==void 0&&(d(s),a=\"\"+s),L(r)&&(d(r.key),a=\"\"+r.key),\"key\"in r){s={};for(var h in r)h!==\"key\"&&(s[h]=r[h])}else s=r;return a&&F(s,typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e),z(e,a,u,l,c(),s,b,y)}function g(e){typeof e==\"object\"&&e!==null&&e.$$typeof===E&&e._store&&(e._store.validated=1)}var _=C(),E=Symbol.for(\"react.transitional.element\"),$=Symbol.for(\"react.portal\"),N=Symbol.for(\"react.fragment\"),G=Symbol.for(\"react.strict_mode\"),X=Symbol.for(\"react.profiler\");Symbol.for(\"react.provider\");var K=Symbol.for(\"react.consumer\"),q=Symbol.for(\"react.context\"),B=Symbol.for(\"react.forward_ref\"),H=Symbol.for(\"react.suspense\"),Q=Symbol.for(\"react.suspense_list\"),Z=Symbol.for(\"react.memo\"),T=Symbol.for(\"react.lazy\"),J=Symbol.for(\"react.activity\"),ee=Symbol.for(\"react.client.reference\"),U=_.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,R=Object.prototype.hasOwnProperty,ne=Array.isArray,S=console.createTask?console.createTask:function(){return null};_={\"react-stack-bottom-frame\":function(e){return e()}};var A,k={},ie=_[\"react-stack-bottom-frame\"].bind(_,w)(),re=S(f(w)),O={};v.Fragment=N,v.jsxDEV=function(e,r,s,o,l,u){var b=1e4>U.recentlyCreatedOwnerStacks++;return V(e,r,s,o,l,u,b?Error(\"react-stack-top-frame\"):ie,b?S(f(e)):re)}})()});var M=x((ve,D)=>{\"use strict\";D.exports=I()});var Ne={};le(Ne,{default:()=>_e,frontmatter:()=>me});var i=ue(M()),me={title:\"Software Engineering\"};function Y(t){let n=Object.assign({p:\"p\",ul:\"ul\",li:\"li\",strong:\"strong\"},t.components),{Accordion:d}=n;return d||pe(\"Accordion\",!0,\"9:1-19:13\"),(0,i.jsxDEV)(i.Fragment,{children:[(0,i.jsxDEV)(n.p,{children:\"We build scalable, production-ready software systems that power the next generation of AI applications.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:5,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.p,{children:\"Our engineering team combines deep technical expertise with pragmatic problem-solving to deliver robust solutions that scale with your business.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:7,columnNumber:1},this),`\n`,(0,i.jsxDEV)(d,{title:\"Full-Stack Development\",children:[(0,i.jsxDEV)(n.p,{children:\"We build complete applications from frontend to backend, with expertise in:\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:11,columnNumber:1},this),(0,i.jsxDEV)(n.ul,{children:[`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Frontend\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:13,columnNumber:3},this),\": React, Next.js, TypeScript, modern CSS frameworks\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Backend\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:14,columnNumber:3},this),\": Node.js, Python, Go, microservices architecture\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:14,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Databases\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:15,columnNumber:3},this),\": PostgreSQL, MongoDB, Redis, vector databases\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:15,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Cloud\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:16,columnNumber:3},this),\": AWS, GCP, Azure, serverless architectures\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:16,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"DevOps\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:17,columnNumber:3},this),\": Docker, Kubernetes, CI/CD, monitoring\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:17,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:13,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:9,columnNumber:1},this),`\n`,(0,i.jsxDEV)(d,{title:\"AI-Native Architecture\",children:[(0,i.jsxDEV)(n.p,{children:\"We design systems specifically optimized for AI workloads:\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:23,columnNumber:1},this),(0,i.jsxDEV)(n.ul,{children:[`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Model Serving\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:25,columnNumber:3},this),\": High-performance inference pipelines\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Vector Search\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:26,columnNumber:3},this),\": Semantic search and retrieval systems\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:26,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Real-time Processing\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:27,columnNumber:3},this),\": Stream processing for live AI applications\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:27,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Scalable Compute\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:28,columnNumber:3},this),\": Auto-scaling infrastructure for variable workloads\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:28,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Data Pipelines\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:29,columnNumber:3},this),\": ETL/ELT systems optimized for ML workflows\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:29,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:25,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:21,columnNumber:1},this),`\n`,(0,i.jsxDEV)(d,{title:\"Enterprise Integration\",children:[(0,i.jsxDEV)(n.p,{children:\"We help organizations integrate AI capabilities into existing systems:\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:35,columnNumber:1},this),(0,i.jsxDEV)(n.ul,{children:[`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"API Development\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:37,columnNumber:3},this),\": RESTful and GraphQL APIs for AI services\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:37,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Legacy Modernization\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:38,columnNumber:3},this),\": Gradual migration to AI-enhanced systems\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:38,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Security & Compliance\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:39,columnNumber:3},this),\": Enterprise-grade security and audit trails\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:39,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Performance Optimization\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:40,columnNumber:3},this),\": System tuning for production workloads\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:40,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Monitoring & Observability\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:41,columnNumber:3},this),\": Comprehensive logging and metrics\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:41,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:37,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:33,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.p,{children:\"Ready to build something extraordinary? Type 'connect' to discuss your project.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:45,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\",lineNumber:1,columnNumber:1},this)}function fe(t={}){let{wrapper:n}=t.components||{};return n?(0,i.jsxDEV)(n,Object.assign({},t,{children:(0,i.jsxDEV)(Y,t,void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx\"},this):Y(t)}var _e=fe;function pe(t,n,d){throw new Error(\"Expected \"+(n?\"component\":\"object\")+\" `\"+t+\"` to be defined: you likely forgot to import, pass, or provide it.\"+(d?\"\\nIt\\u2019s referenced in your code at `\"+d+\"` in `/Users/<USER>/espirai-website/content/services/_mdx_bundler_entry_point-b139eb4a-e464-4509-a2e6-d194bf1d7e1c.mdx`\":\"\"))}return be(Ne);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "services/software-engineering.mdx", "_raw": {"sourceFilePath": "services/software-engineering.mdx", "sourceFileName": "software-engineering.mdx", "sourceFileDir": "services", "contentType": "mdx", "flattenedPath": "services/software-engineering"}, "type": "Page", "slug": "services/software-engineering"}, {"title": "About", "body": {"raw": "\nWe're building the next-gen operating system for AI agents.\n\nModern AI will fundamentally change how people use software in their daily lives. Agentic applications could, for the first time, enable computers to help people in much the same way people help each other.\n\nBut it won't happen without new UI patterns, a reimagined privacy model, and a developer platform that makes it possible to build robust consumer experiences and ship radically more capable apps. That's the challenge we're taking on.\n\nWant to know more? Type 'connect' to reach out.\n", "code": "var Component=(()=>{var ae=Object.create;var _=Object.defineProperty;var ie=Object.getOwnPropertyDescriptor;var se=Object.getOwnPropertyNames;var ce=Object.getPrototypeOf,le=Object.prototype.hasOwnProperty;var y=(n,t)=>()=>(t||n((t={exports:{}}).exports,t),t.exports),ue=(n,t)=>{for(var s in t)_(n,s,{get:t[s],enumerable:!0})},P=(n,t,s,m)=>{if(t&&typeof t==\"object\"||typeof t==\"function\")for(let l of se(t))!le.call(n,l)&&l!==s&&_(n,l,{get:()=>t[l],enumerable:!(m=ie(t,l))||m.enumerable});return n};var fe=(n,t,s)=>(s=n!=null?ae(ce(n)):{},P(t||!n||!n.__esModule?_(s,\"default\",{value:n,enumerable:!0}):s,n)),de=n=>P(_({},\"__esModule\",{value:!0}),n);var D=y((he,C)=>{C.exports=React});var I=y(T=>{\"use strict\";(function(){function n(e){if(e==null)return null;if(typeof e==\"function\")return e.$$typeof===ee?null:e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case E:return\"Fragment\";case G:return\"Profiler\";case X:return\"StrictMode\";case H:return\"Suspense\";case Z:return\"SuspenseList\";case J:return\"Activity\"}if(typeof e==\"object\")switch(typeof e.tag==\"number\"&&console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),e.$$typeof){case z:return\"Portal\";case K:return(e.displayName||\"Context\")+\".Provider\";case q:return(e._context.displayName||\"Context\")+\".Consumer\";case B:var r=e.render;return e=e.displayName,e||(e=r.displayName||r.name||\"\",e=e!==\"\"?\"ForwardRef(\"+e+\")\":\"ForwardRef\"),e;case Q:return r=e.displayName||null,r!==null?r:n(e.type)||\"Memo\";case N:r=e._payload,e=e._init;try{return n(e(r))}catch{}}return null}function t(e){return\"\"+e}function s(e){try{t(e);var r=!1}catch{r=!0}if(r){r=console;var o=r.error,i=typeof Symbol==\"function\"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return o.call(r,\"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",i),t(e)}}function m(e){if(e===E)return\"<>\";if(typeof e==\"object\"&&e!==null&&e.$$typeof===N)return\"<...>\";try{var r=n(e);return r?\"<\"+r+\">\":\"<...>\"}catch{return\"<...>\"}}function l(){var e=O.A;return e===null?null:e.getOwner()}function w(){return Error(\"react-stack-top-frame\")}function M(e){if(k.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function W(e,r){function o(){x||(x=!0,console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",r))}o.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:o,configurable:!0})}function L(){var e=n(this.type);return S[e]||(S[e]=!0,console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\")),e=this.props.ref,e!==void 0?e:null}function $(e,r,o,i,u,f,d,h){return o=f.ref,e={$$typeof:v,type:e,key:r,props:f,_owner:u},(o!==void 0?o:null)!==null?Object.defineProperty(e,\"ref\",{enumerable:!1,get:L}):Object.defineProperty(e,\"ref\",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,\"_debugInfo\",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,\"_debugStack\",{configurable:!1,enumerable:!1,writable:!0,value:d}),Object.defineProperty(e,\"_debugTask\",{configurable:!1,enumerable:!1,writable:!0,value:h}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function V(e,r,o,i,u,f,d,h){var a=r.children;if(a!==void 0)if(i)if(re(a)){for(i=0;i<a.length;i++)R(a[i]);Object.freeze&&Object.freeze(a)}else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else R(a);if(k.call(r,\"key\")){a=n(e);var b=Object.keys(r).filter(function(oe){return oe!==\"key\"});i=0<b.length?\"{key: someKey, \"+b.join(\": ..., \")+\": ...}\":\"{key: someKey}\",j[a+i]||(b=0<b.length?\"{\"+b.join(\": ..., \")+\": ...}\":\"{}\",console.error(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,i,a,b,a),j[a+i]=!0)}if(a=null,o!==void 0&&(s(o),a=\"\"+o),M(r)&&(s(r.key),a=\"\"+r.key),\"key\"in r){o={};for(var g in r)g!==\"key\"&&(o[g]=r[g])}else o=r;return a&&W(o,typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e),$(e,a,f,u,l(),o,d,h)}function R(e){typeof e==\"object\"&&e!==null&&e.$$typeof===v&&e._store&&(e._store.validated=1)}var p=D(),v=Symbol.for(\"react.transitional.element\"),z=Symbol.for(\"react.portal\"),E=Symbol.for(\"react.fragment\"),X=Symbol.for(\"react.strict_mode\"),G=Symbol.for(\"react.profiler\");Symbol.for(\"react.provider\");var q=Symbol.for(\"react.consumer\"),K=Symbol.for(\"react.context\"),B=Symbol.for(\"react.forward_ref\"),H=Symbol.for(\"react.suspense\"),Z=Symbol.for(\"react.suspense_list\"),Q=Symbol.for(\"react.memo\"),N=Symbol.for(\"react.lazy\"),J=Symbol.for(\"react.activity\"),ee=Symbol.for(\"react.client.reference\"),O=p.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,k=Object.prototype.hasOwnProperty,re=Array.isArray,A=console.createTask?console.createTask:function(){return null};p={\"react-stack-bottom-frame\":function(e){return e()}};var x,S={},ne=p[\"react-stack-bottom-frame\"].bind(p,w)(),te=A(m(w)),j={};T.Fragment=E,T.jsxDEV=function(e,r,o,i,u,f){var d=1e4>O.recentlyCreatedOwnerStacks++;return V(e,r,o,i,u,f,d?Error(\"react-stack-top-frame\"):ne,d?A(m(e)):te)}})()});var Y=y((ye,U)=>{\"use strict\";U.exports=I()});var _e={};ue(_e,{default:()=>pe,frontmatter:()=>be});var c=fe(Y()),be={title:\"About\"};function F(n){let t=Object.assign({p:\"p\"},n.components);return(0,c.jsxDEV)(c.Fragment,{children:[(0,c.jsxDEV)(t.p,{children:\"We're building the next-gen operating system for AI agents.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-29b89b7b-bfc2-4d7e-a201-902e72f25732.mdx\",lineNumber:5,columnNumber:1},this),`\n`,(0,c.jsxDEV)(t.p,{children:\"Modern AI will fundamentally change how people use software in their daily lives. Agentic applications could, for the first time, enable computers to help people in much the same way people help each other.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-29b89b7b-bfc2-4d7e-a201-902e72f25732.mdx\",lineNumber:7,columnNumber:1},this),`\n`,(0,c.jsxDEV)(t.p,{children:\"But it won't happen without new UI patterns, a reimagined privacy model, and a developer platform that makes it possible to build robust consumer experiences and ship radically more capable apps. That's the challenge we're taking on.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-29b89b7b-bfc2-4d7e-a201-902e72f25732.mdx\",lineNumber:9,columnNumber:1},this),`\n`,(0,c.jsxDEV)(t.p,{children:\"Want to know more? Type 'connect' to reach out.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-29b89b7b-bfc2-4d7e-a201-902e72f25732.mdx\",lineNumber:11,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-29b89b7b-bfc2-4d7e-a201-902e72f25732.mdx\",lineNumber:1,columnNumber:1},this)}function me(n={}){let{wrapper:t}=n.components||{};return t?(0,c.jsxDEV)(t,Object.assign({},n,{children:(0,c.jsxDEV)(F,n,void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-29b89b7b-bfc2-4d7e-a201-902e72f25732.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-29b89b7b-bfc2-4d7e-a201-902e72f25732.mdx\"},this):F(n)}var pe=me;return de(_e);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "pages/about.mdx", "_raw": {"sourceFilePath": "pages/about.mdx", "sourceFileName": "about.mdx", "sourceFileDir": "pages", "contentType": "mdx", "flattenedPath": "pages/about"}, "type": "Page", "slug": "pages/about"}, {"title": "Interactive Demo", "body": {"raw": "\n# AI Agent Platform Demo\n\nExperience the next-generation operating system for AI agents through interactive examples.\n\n<CodePlayground\n  title=\"Create Your First AI Agent\"\n  description=\"Try modifying the agent configuration and see how it affects the execution\"\n  initialCode={`const agent = new Agent({\n  name: 'data-processor',\n  capabilities: ['file-processing', 'api-calls'],\n  task: async (input) => {\n    // Process incoming data\n    const result = await processData(input);\n\n    // Return structured output\n    return {\n      status: 'completed',\n      data: result,\n      timestamp: new Date().toISOString()\n    };\n  }\n});\n\n// Execute the agent\nawait agent.execute({\n  source: 'database',\n  format: 'json'\n});`}\n/>\n\n<Accordion title=\"Agent Architecture Overview\">\n\nOur platform enables AI agents to operate with unprecedented autonomy and coordination:\n\n**Core Components:**\n- **Agent Runtime**: Secure execution environment for AI agents\n- **Communication Layer**: Inter-agent messaging and coordination\n- **Resource Management**: Dynamic allocation of compute and memory\n- **Security Framework**: Sandboxed execution with permission controls\n\n**Key Features:**\n- Real-time agent orchestration\n- Scalable multi-agent workflows  \n- Built-in monitoring and observability\n- Enterprise-grade security and compliance\n\n</Accordion>\n\n<Accordion title=\"Example: Multi-Agent Data Pipeline\">\n\nHere's how multiple AI agents collaborate to process and analyze data:\n\n```typescript\n// Agent 1: Data Ingestion\nconst ingestAgent = new Agent({\n  name: 'data-ingest',\n  capabilities: ['file-processing', 'api-calls'],\n  task: async (input) => {\n    const rawData = await fetchFromSources(input.sources);\n    return { data: rawData, status: 'ingested' };\n  }\n});\n\n// Agent 2: Data Processing  \nconst processAgent = new Agent({\n  name: 'data-processor',\n  capabilities: ['data-transformation', 'validation'],\n  task: async (input) => {\n    const cleanData = await transform(input.data);\n    return { processedData: cleanData, status: 'processed' };\n  }\n});\n\n// Agent 3: Analysis & Insights\nconst analysisAgent = new Agent({\n  name: 'data-analyst',\n  capabilities: ['ml-inference', 'pattern-recognition'],\n  task: async (input) => {\n    const insights = await generateInsights(input.processedData);\n    return { insights, recommendations: insights.recommendations };\n  }\n});\n\n// Orchestration\nconst pipeline = new AgentPipeline([\n  ingestAgent,\n  processAgent, \n  analysisAgent\n]);\n\nawait pipeline.execute({ sources: ['api', 'database', 'files'] });\n```\n\nThis demonstrates how agents can work together autonomously while maintaining clear boundaries and responsibilities.\n\n</Accordion>\n\n<Accordion title=\"Real-World Use Cases\">\n\n**Customer Support Automation**\n- Agent 1: Ticket classification and routing\n- Agent 2: Knowledge base search and response generation  \n- Agent 3: Escalation handling and human handoff\n\n**Financial Analysis Platform**\n- Agent 1: Market data aggregation\n- Agent 2: Risk assessment and modeling\n- Agent 3: Report generation and alerts\n\n**Content Management System**\n- Agent 1: Content ingestion and parsing\n- Agent 2: SEO optimization and metadata generation\n- Agent 3: Publishing and distribution\n\nEach use case demonstrates how our platform enables sophisticated AI workflows that scale with your business needs.\n\n</Accordion>\n\nReady to build your own AI agent platform? Type 'connect' to discuss your specific requirements.\n", "code": "var Component=(()=>{var se=Object.create;var _=Object.defineProperty;var oe=Object.getOwnPropertyDescriptor;var de=Object.getOwnPropertyNames;var le=Object.getPrototypeOf,ce=Object.prototype.hasOwnProperty;var h=(r,n)=>()=>(n||r((n={exports:{}}).exports,n),n.exports),ue=(r,n)=>{for(var s in n)_(r,s,{get:n[s],enumerable:!0})},P=(r,n,s,l)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let c of de(n))!ce.call(r,c)&&c!==s&&_(r,c,{get:()=>n[c],enumerable:!(l=oe(n,c))||l.enumerable});return r};var me=(r,n,s)=>(s=r!=null?se(le(r)):{},P(n||!r||!r.__esModule?_(s,\"default\",{value:r,enumerable:!0}):s,r)),be=r=>P(_({},\"__esModule\",{value:!0}),r);var C=h((ye,j)=>{j.exports=React});var D=h(x=>{\"use strict\";(function(){function r(e){if(e==null)return null;if(typeof e==\"function\")return e.$$typeof===ne?null:e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case g:return\"Fragment\";case q:return\"Profiler\";case K:return\"StrictMode\";case Z:return\"Suspense\";case Q:return\"SuspenseList\";case ee:return\"Activity\"}if(typeof e==\"object\")switch(typeof e.tag==\"number\"&&console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),e.$$typeof){case X:return\"Portal\";case B:return(e.displayName||\"Context\")+\".Provider\";case G:return(e._context.displayName||\"Context\")+\".Consumer\";case H:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||\"\",e=e!==\"\"?\"ForwardRef(\"+e+\")\":\"ForwardRef\"),e;case J:return t=e.displayName||null,t!==null?t:r(e.type)||\"Memo\";case U:t=e._payload,e=e._init;try{return r(e(t))}catch{}}return null}function n(e){return\"\"+e}function s(e){try{n(e);var t=!1}catch{t=!0}if(t){t=console;var a=t.error,d=typeof Symbol==\"function\"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return a.call(t,\"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",d),n(e)}}function l(e){if(e===g)return\"<>\";if(typeof e==\"object\"&&e!==null&&e.$$typeof===U)return\"<...>\";try{var t=r(e);return t?\"<\"+t+\">\":\"<...>\"}catch{return\"<...>\"}}function c(){var e=R.A;return e===null?null:e.getOwner()}function w(){return Error(\"react-stack-top-frame\")}function L(e){if(T.call(e,\"key\")){var t=Object.getOwnPropertyDescriptor(e,\"key\").get;if(t&&t.isReactWarning)return!1}return e.key!==void 0}function W(e,t){function a(){k||(k=!0,console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",t))}a.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:a,configurable:!0})}function $(){var e=r(this.type);return O[e]||(O[e]=!0,console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\")),e=this.props.ref,e!==void 0?e:null}function V(e,t,a,d,u,m,b,N){return a=m.ref,e={$$typeof:E,type:e,key:t,props:m,_owner:u},(a!==void 0?a:null)!==null?Object.defineProperty(e,\"ref\",{enumerable:!1,get:$}):Object.defineProperty(e,\"ref\",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,\"_debugInfo\",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,\"_debugStack\",{configurable:!1,enumerable:!1,writable:!0,value:b}),Object.defineProperty(e,\"_debugTask\",{configurable:!1,enumerable:!1,writable:!0,value:N}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function z(e,t,a,d,u,m,b,N){var o=t.children;if(o!==void 0)if(d)if(ie(o)){for(d=0;d<o.length;d++)A(o[d]);Object.freeze&&Object.freeze(o)}else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else A(o);if(T.call(t,\"key\")){o=r(e);var f=Object.keys(t).filter(function(ae){return ae!==\"key\"});d=0<f.length?\"{key: someKey, \"+f.join(\": ..., \")+\": ...}\":\"{key: someKey}\",S[o+d]||(f=0<f.length?\"{\"+f.join(\": ..., \")+\": ...}\":\"{}\",console.error(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,d,o,f,o),S[o+d]=!0)}if(o=null,a!==void 0&&(s(a),o=\"\"+a),L(t)&&(s(t.key),o=\"\"+t.key),\"key\"in t){a={};for(var y in t)y!==\"key\"&&(a[y]=t[y])}else a=t;return o&&W(a,typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e),V(e,o,m,u,c(),a,b,N)}function A(e){typeof e==\"object\"&&e!==null&&e.$$typeof===E&&e._store&&(e._store.validated=1)}var p=C(),E=Symbol.for(\"react.transitional.element\"),X=Symbol.for(\"react.portal\"),g=Symbol.for(\"react.fragment\"),K=Symbol.for(\"react.strict_mode\"),q=Symbol.for(\"react.profiler\");Symbol.for(\"react.provider\");var G=Symbol.for(\"react.consumer\"),B=Symbol.for(\"react.context\"),H=Symbol.for(\"react.forward_ref\"),Z=Symbol.for(\"react.suspense\"),Q=Symbol.for(\"react.suspense_list\"),J=Symbol.for(\"react.memo\"),U=Symbol.for(\"react.lazy\"),ee=Symbol.for(\"react.activity\"),ne=Symbol.for(\"react.client.reference\"),R=p.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,T=Object.prototype.hasOwnProperty,ie=Array.isArray,v=console.createTask?console.createTask:function(){return null};p={\"react-stack-bottom-frame\":function(e){return e()}};var k,O={},te=p[\"react-stack-bottom-frame\"].bind(p,w)(),re=v(l(w)),S={};x.Fragment=g,x.jsxDEV=function(e,t,a,d,u,m){var b=1e4>R.recentlyCreatedOwnerStacks++;return z(e,t,a,d,u,m,b?Error(\"react-stack-top-frame\"):te,b?v(l(e)):re)}})()});var F=h((xe,I)=>{\"use strict\";I.exports=D()});var ge={};ue(ge,{default:()=>_e,frontmatter:()=>fe});var i=me(F()),fe={title:\"Interactive Demo\"};function Y(r){let n=Object.assign({h1:\"h1\",p:\"p\",strong:\"strong\",ul:\"ul\",li:\"li\",pre:\"pre\",code:\"code\"},r.components),{CodePlayground:s,Accordion:l}=n;return l||M(\"Accordion\",!0,\"35:1-51:13\"),s||M(\"CodePlayground\",!0,\"9:1-33:3\"),(0,i.jsxDEV)(i.Fragment,{children:[(0,i.jsxDEV)(n.h1,{children:\"AI Agent Platform Demo\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:5,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.p,{children:\"Experience the next-generation operating system for AI agents through interactive examples.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:7,columnNumber:1},this),`\n`,(0,i.jsxDEV)(s,{title:\"Create Your First AI Agent\",description:\"Try modifying the agent configuration and see how it affects the execution\",initialCode:`const agent = new Agent({\n  name: 'data-processor',\n  capabilities: ['file-processing', 'api-calls'],\n  task: async (input) => {\n    // Process incoming data\n    const result = await processData(input);\n\n    // Return structured output\n    return {\n      status: 'completed',\n      data: result,\n      timestamp: new Date().toISOString()\n    };\n  }\n});\n\n// Execute the agent\nawait agent.execute({\n  source: 'database',\n  format: 'json'\n});`},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:9,columnNumber:1},this),`\n`,(0,i.jsxDEV)(l,{title:\"Agent Architecture Overview\",children:[(0,i.jsxDEV)(n.p,{children:\"Our platform enables AI agents to operate with unprecedented autonomy and coordination:\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:37,columnNumber:1},this),(0,i.jsxDEV)(n.p,{children:(0,i.jsxDEV)(n.strong,{children:\"Core Components:\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:39,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:39,columnNumber:1},this),(0,i.jsxDEV)(n.ul,{children:[`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Agent Runtime\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:40,columnNumber:3},this),\": Secure execution environment for AI agents\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:40,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Communication Layer\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:41,columnNumber:3},this),\": Inter-agent messaging and coordination\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:41,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Resource Management\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:42,columnNumber:3},this),\": Dynamic allocation of compute and memory\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:42,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:[(0,i.jsxDEV)(n.strong,{children:\"Security Framework\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:43,columnNumber:3},this),\": Sandboxed execution with permission controls\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:43,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:40,columnNumber:1},this),(0,i.jsxDEV)(n.p,{children:(0,i.jsxDEV)(n.strong,{children:\"Key Features:\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:45,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:45,columnNumber:1},this),(0,i.jsxDEV)(n.ul,{children:[`\n`,(0,i.jsxDEV)(n.li,{children:\"Real-time agent orchestration\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:46,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:\"Scalable multi-agent workflows\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:47,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:\"Built-in monitoring and observability\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:48,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:\"Enterprise-grade security and compliance\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:49,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:46,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:35,columnNumber:1},this),`\n`,(0,i.jsxDEV)(l,{title:\"Example: Multi-Agent Data Pipeline\",children:[(0,i.jsxDEV)(n.p,{children:\"Here's how multiple AI agents collaborate to process and analyze data:\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:55,columnNumber:1},this),(0,i.jsxDEV)(n.pre,{children:(0,i.jsxDEV)(n.code,{className:\"language-typescript\",children:`// Agent 1: Data Ingestion\nconst ingestAgent = new Agent({\n  name: 'data-ingest',\n  capabilities: ['file-processing', 'api-calls'],\n  task: async (input) => {\n    const rawData = await fetchFromSources(input.sources);\n    return { data: rawData, status: 'ingested' };\n  }\n});\n\n// Agent 2: Data Processing  \nconst processAgent = new Agent({\n  name: 'data-processor',\n  capabilities: ['data-transformation', 'validation'],\n  task: async (input) => {\n    const cleanData = await transform(input.data);\n    return { processedData: cleanData, status: 'processed' };\n  }\n});\n\n// Agent 3: Analysis & Insights\nconst analysisAgent = new Agent({\n  name: 'data-analyst',\n  capabilities: ['ml-inference', 'pattern-recognition'],\n  task: async (input) => {\n    const insights = await generateInsights(input.processedData);\n    return { insights, recommendations: insights.recommendations };\n  }\n});\n\n// Orchestration\nconst pipeline = new AgentPipeline([\n  ingestAgent,\n  processAgent, \n  analysisAgent\n]);\n\nawait pipeline.execute({ sources: ['api', 'database', 'files'] });\n`},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:57,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:57,columnNumber:1},this),(0,i.jsxDEV)(n.p,{children:\"This demonstrates how agents can work together autonomously while maintaining clear boundaries and responsibilities.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:98,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:53,columnNumber:1},this),`\n`,(0,i.jsxDEV)(l,{title:\"Real-World Use Cases\",children:[(0,i.jsxDEV)(n.p,{children:(0,i.jsxDEV)(n.strong,{children:\"Customer Support Automation\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:104,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:104,columnNumber:1},this),(0,i.jsxDEV)(n.ul,{children:[`\n`,(0,i.jsxDEV)(n.li,{children:\"Agent 1: Ticket classification and routing\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:105,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:\"Agent 2: Knowledge base search and response generation\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:106,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:\"Agent 3: Escalation handling and human handoff\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:107,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:105,columnNumber:1},this),(0,i.jsxDEV)(n.p,{children:(0,i.jsxDEV)(n.strong,{children:\"Financial Analysis Platform\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:109,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:109,columnNumber:1},this),(0,i.jsxDEV)(n.ul,{children:[`\n`,(0,i.jsxDEV)(n.li,{children:\"Agent 1: Market data aggregation\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:110,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:\"Agent 2: Risk assessment and modeling\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:111,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:\"Agent 3: Report generation and alerts\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:112,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:110,columnNumber:1},this),(0,i.jsxDEV)(n.p,{children:(0,i.jsxDEV)(n.strong,{children:\"Content Management System\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:114,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:114,columnNumber:1},this),(0,i.jsxDEV)(n.ul,{children:[`\n`,(0,i.jsxDEV)(n.li,{children:\"Agent 1: Content ingestion and parsing\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:115,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:\"Agent 2: SEO optimization and metadata generation\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:116,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.li,{children:\"Agent 3: Publishing and distribution\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:117,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:115,columnNumber:1},this),(0,i.jsxDEV)(n.p,{children:\"Each use case demonstrates how our platform enables sophisticated AI workflows that scale with your business needs.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:119,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:102,columnNumber:1},this),`\n`,(0,i.jsxDEV)(n.p,{children:\"Ready to build your own AI agent platform? Type 'connect' to discuss your specific requirements.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:123,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\",lineNumber:1,columnNumber:1},this)}function pe(r={}){let{wrapper:n}=r.components||{};return n?(0,i.jsxDEV)(n,Object.assign({},r,{children:(0,i.jsxDEV)(Y,r,void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx\"},this):Y(r)}var _e=pe;function M(r,n,s){throw new Error(\"Expected \"+(n?\"component\":\"object\")+\" `\"+r+\"` to be defined: you likely forgot to import, pass, or provide it.\"+(s?\"\\nIt\\u2019s referenced in your code at `\"+s+\"` in `/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-453b70ef-5b60-4776-80e6-cf26c37a69a6.mdx`\":\"\"))}return be(ge);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "pages/demo.mdx", "_raw": {"sourceFilePath": "pages/demo.mdx", "sourceFileName": "demo.mdx", "sourceFileDir": "pages", "contentType": "mdx", "flattenedPath": "pages/demo"}, "type": "Page", "slug": "pages/demo"}, {"title": "Jobs", "body": {"raw": "\nWe're looking for user-centric, craft-focused, pioneering minds who don't take themselves too seriously to join our team.\n\nWe're ambitious yet pragmatic. We run fast but sweat the details. We think the best way to invent the future is by relentlessly making progress every day.\n\nReady to join us? Type 'connect' to get in touch.", "code": "var Component=(()=>{var ae=Object.create;var p=Object.defineProperty;var ie=Object.getOwnPropertyDescriptor;var se=Object.getOwnPropertyNames;var ce=Object.getPrototypeOf,le=Object.prototype.hasOwnProperty;var T=(t,n)=>()=>(n||t((n={exports:{}}).exports,n),n.exports),ue=(t,n)=>{for(var s in n)p(t,s,{get:n[s],enumerable:!0})},P=(t,n,s,m)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let l of se(n))!le.call(t,l)&&l!==s&&p(t,l,{get:()=>n[l],enumerable:!(m=ie(n,l))||m.enumerable});return t};var fe=(t,n,s)=>(s=t!=null?ae(ce(t)):{},P(n||!t||!t.__esModule?p(s,\"default\",{value:t,enumerable:!0}):s,t)),de=t=>P(p({},\"__esModule\",{value:!0}),t);var D=T((ge,C)=>{C.exports=React});var Y=T(R=>{\"use strict\";(function(){function t(e){if(e==null)return null;if(typeof e==\"function\")return e.$$typeof===ee?null:e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case E:return\"Fragment\";case G:return\"Profiler\";case X:return\"StrictMode\";case H:return\"Suspense\";case Z:return\"SuspenseList\";case Q:return\"Activity\"}if(typeof e==\"object\")switch(typeof e.tag==\"number\"&&console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),e.$$typeof){case z:return\"Portal\";case K:return(e.displayName||\"Context\")+\".Provider\";case q:return(e._context.displayName||\"Context\")+\".Consumer\";case B:var r=e.render;return e=e.displayName,e||(e=r.displayName||r.name||\"\",e=e!==\"\"?\"ForwardRef(\"+e+\")\":\"ForwardRef\"),e;case J:return r=e.displayName||null,r!==null?r:t(e.type)||\"Memo\";case N:r=e._payload,e=e._init;try{return t(e(r))}catch{}}return null}function n(e){return\"\"+e}function s(e){try{n(e);var r=!1}catch{r=!0}if(r){r=console;var o=r.error,i=typeof Symbol==\"function\"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return o.call(r,\"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",i),n(e)}}function m(e){if(e===E)return\"<>\";if(typeof e==\"object\"&&e!==null&&e.$$typeof===N)return\"<...>\";try{var r=t(e);return r?\"<\"+r+\">\":\"<...>\"}catch{return\"<...>\"}}function l(){var e=O.A;return e===null?null:e.getOwner()}function h(){return Error(\"react-stack-top-frame\")}function F(e){if(k.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function M(e,r){function o(){S||(S=!0,console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",r))}o.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:o,configurable:!0})}function L(){var e=t(this.type);return A[e]||(A[e]=!0,console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\")),e=this.props.ref,e!==void 0?e:null}function $(e,r,o,i,u,f,d,g){return o=f.ref,e={$$typeof:w,type:e,key:r,props:f,_owner:u},(o!==void 0?o:null)!==null?Object.defineProperty(e,\"ref\",{enumerable:!1,get:L}):Object.defineProperty(e,\"ref\",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,\"_debugInfo\",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,\"_debugStack\",{configurable:!1,enumerable:!1,writable:!0,value:d}),Object.defineProperty(e,\"_debugTask\",{configurable:!1,enumerable:!1,writable:!0,value:g}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function V(e,r,o,i,u,f,d,g){var a=r.children;if(a!==void 0)if(i)if(re(a)){for(i=0;i<a.length;i++)v(a[i]);Object.freeze&&Object.freeze(a)}else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else v(a);if(k.call(r,\"key\")){a=t(e);var b=Object.keys(r).filter(function(oe){return oe!==\"key\"});i=0<b.length?\"{key: someKey, \"+b.join(\": ..., \")+\": ...}\":\"{key: someKey}\",x[a+i]||(b=0<b.length?\"{\"+b.join(\": ..., \")+\": ...}\":\"{}\",console.error(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,i,a,b,a),x[a+i]=!0)}if(a=null,o!==void 0&&(s(o),a=\"\"+o),F(r)&&(s(r.key),a=\"\"+r.key),\"key\"in r){o={};for(var y in r)y!==\"key\"&&(o[y]=r[y])}else o=r;return a&&M(o,typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e),$(e,a,f,u,l(),o,d,g)}function v(e){typeof e==\"object\"&&e!==null&&e.$$typeof===w&&e._store&&(e._store.validated=1)}var _=D(),w=Symbol.for(\"react.transitional.element\"),z=Symbol.for(\"react.portal\"),E=Symbol.for(\"react.fragment\"),X=Symbol.for(\"react.strict_mode\"),G=Symbol.for(\"react.profiler\");Symbol.for(\"react.provider\");var q=Symbol.for(\"react.consumer\"),K=Symbol.for(\"react.context\"),B=Symbol.for(\"react.forward_ref\"),H=Symbol.for(\"react.suspense\"),Z=Symbol.for(\"react.suspense_list\"),J=Symbol.for(\"react.memo\"),N=Symbol.for(\"react.lazy\"),Q=Symbol.for(\"react.activity\"),ee=Symbol.for(\"react.client.reference\"),O=_.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,k=Object.prototype.hasOwnProperty,re=Array.isArray,j=console.createTask?console.createTask:function(){return null};_={\"react-stack-bottom-frame\":function(e){return e()}};var S,A={},te=_[\"react-stack-bottom-frame\"].bind(_,h)(),ne=j(m(h)),x={};R.Fragment=E,R.jsxDEV=function(e,r,o,i,u,f){var d=1e4>O.recentlyCreatedOwnerStacks++;return V(e,r,o,i,u,f,d?Error(\"react-stack-top-frame\"):te,d?j(m(e)):ne)}})()});var I=T((Te,U)=>{\"use strict\";U.exports=Y()});var pe={};ue(pe,{default:()=>_e,frontmatter:()=>be});var c=fe(I()),be={title:\"Jobs\"};function W(t){let n=Object.assign({p:\"p\"},t.components);return(0,c.jsxDEV)(c.Fragment,{children:[(0,c.jsxDEV)(n.p,{children:\"We're looking for user-centric, craft-focused, pioneering minds who don't take themselves too seriously to join our team.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-d5046a54-b378-4ee8-936a-9bf964f64abc.mdx\",lineNumber:5,columnNumber:1},this),`\n`,(0,c.jsxDEV)(n.p,{children:\"We're ambitious yet pragmatic. We run fast but sweat the details. We think the best way to invent the future is by relentlessly making progress every day.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-d5046a54-b378-4ee8-936a-9bf964f64abc.mdx\",lineNumber:7,columnNumber:1},this),`\n`,(0,c.jsxDEV)(n.p,{children:\"Ready to join us? Type 'connect' to get in touch.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-d5046a54-b378-4ee8-936a-9bf964f64abc.mdx\",lineNumber:9,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-d5046a54-b378-4ee8-936a-9bf964f64abc.mdx\",lineNumber:1,columnNumber:1},this)}function me(t={}){let{wrapper:n}=t.components||{};return n?(0,c.jsxDEV)(n,Object.assign({},t,{children:(0,c.jsxDEV)(W,t,void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-d5046a54-b378-4ee8-936a-9bf964f64abc.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-d5046a54-b378-4ee8-936a-9bf964f64abc.mdx\"},this):W(t)}var _e=me;return de(pe);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "pages/jobs.mdx", "_raw": {"sourceFilePath": "pages/jobs.mdx", "sourceFileName": "jobs.mdx", "sourceFileDir": "pages", "contentType": "mdx", "flattenedPath": "pages/jobs"}, "type": "Page", "slug": "pages/jobs"}, {"title": "People", "body": {"raw": "\nEspirAI is a collective of expert AI and software engineers, designers, and product builders.\n\nWe're distributed across time zones but united by a shared vision: building the infrastructure that enables the next generation of intelligent applications.\n\nOur team combines deep technical expertise with a pragmatic approach to solving real-world problems.", "code": "var Component=(()=>{var oe=Object.create;var _=Object.defineProperty;var ie=Object.getOwnPropertyDescriptor;var se=Object.getOwnPropertyNames;var ce=Object.getPrototypeOf,le=Object.prototype.hasOwnProperty;var R=(t,n)=>()=>(n||t((n={exports:{}}).exports,n),n.exports),ue=(t,n)=>{for(var s in n)_(t,s,{get:n[s],enumerable:!0})},P=(t,n,s,b)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let l of se(n))!le.call(t,l)&&l!==s&&_(t,l,{get:()=>n[l],enumerable:!(b=ie(n,l))||b.enumerable});return t};var fe=(t,n,s)=>(s=t!=null?oe(ce(t)):{},P(n||!t||!t.__esModule?_(s,\"default\",{value:t,enumerable:!0}):s,t)),de=t=>P(_({},\"__esModule\",{value:!0}),t);var D=R((ge,C)=>{C.exports=React});var Y=R(h=>{\"use strict\";(function(){function t(e){if(e==null)return null;if(typeof e==\"function\")return e.$$typeof===ee?null:e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case E:return\"Fragment\";case G:return\"Profiler\";case X:return\"StrictMode\";case H:return\"Suspense\";case Z:return\"SuspenseList\";case J:return\"Activity\"}if(typeof e==\"object\")switch(typeof e.tag==\"number\"&&console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),e.$$typeof){case z:return\"Portal\";case K:return(e.displayName||\"Context\")+\".Provider\";case q:return(e._context.displayName||\"Context\")+\".Consumer\";case B:var r=e.render;return e=e.displayName,e||(e=r.displayName||r.name||\"\",e=e!==\"\"?\"ForwardRef(\"+e+\")\":\"ForwardRef\"),e;case Q:return r=e.displayName||null,r!==null?r:t(e.type)||\"Memo\";case N:r=e._payload,e=e._init;try{return t(e(r))}catch{}}return null}function n(e){return\"\"+e}function s(e){try{n(e);var r=!1}catch{r=!0}if(r){r=console;var a=r.error,i=typeof Symbol==\"function\"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return a.call(r,\"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",i),n(e)}}function b(e){if(e===E)return\"<>\";if(typeof e==\"object\"&&e!==null&&e.$$typeof===N)return\"<...>\";try{var r=t(e);return r?\"<\"+r+\">\":\"<...>\"}catch{return\"<...>\"}}function l(){var e=O.A;return e===null?null:e.getOwner()}function v(){return Error(\"react-stack-top-frame\")}function M(e){if(x.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function W(e,r){function a(){k||(k=!0,console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",r))}a.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:a,configurable:!0})}function L(){var e=t(this.type);return S[e]||(S[e]=!0,console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\")),e=this.props.ref,e!==void 0?e:null}function $(e,r,a,i,u,f,d,g){return a=f.ref,e={$$typeof:w,type:e,key:r,props:f,_owner:u},(a!==void 0?a:null)!==null?Object.defineProperty(e,\"ref\",{enumerable:!1,get:L}):Object.defineProperty(e,\"ref\",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,\"_debugInfo\",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,\"_debugStack\",{configurable:!1,enumerable:!1,writable:!0,value:d}),Object.defineProperty(e,\"_debugTask\",{configurable:!1,enumerable:!1,writable:!0,value:g}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function V(e,r,a,i,u,f,d,g){var o=r.children;if(o!==void 0)if(i)if(re(o)){for(i=0;i<o.length;i++)y(o[i]);Object.freeze&&Object.freeze(o)}else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else y(o);if(x.call(r,\"key\")){o=t(e);var m=Object.keys(r).filter(function(ae){return ae!==\"key\"});i=0<m.length?\"{key: someKey, \"+m.join(\": ..., \")+\": ...}\":\"{key: someKey}\",j[o+i]||(m=0<m.length?\"{\"+m.join(\": ..., \")+\": ...}\":\"{}\",console.error(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,i,o,m,o),j[o+i]=!0)}if(o=null,a!==void 0&&(s(a),o=\"\"+a),M(r)&&(s(r.key),o=\"\"+r.key),\"key\"in r){a={};for(var T in r)T!==\"key\"&&(a[T]=r[T])}else a=r;return o&&W(a,typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e),$(e,o,f,u,l(),a,d,g)}function y(e){typeof e==\"object\"&&e!==null&&e.$$typeof===w&&e._store&&(e._store.validated=1)}var p=D(),w=Symbol.for(\"react.transitional.element\"),z=Symbol.for(\"react.portal\"),E=Symbol.for(\"react.fragment\"),X=Symbol.for(\"react.strict_mode\"),G=Symbol.for(\"react.profiler\");Symbol.for(\"react.provider\");var q=Symbol.for(\"react.consumer\"),K=Symbol.for(\"react.context\"),B=Symbol.for(\"react.forward_ref\"),H=Symbol.for(\"react.suspense\"),Z=Symbol.for(\"react.suspense_list\"),Q=Symbol.for(\"react.memo\"),N=Symbol.for(\"react.lazy\"),J=Symbol.for(\"react.activity\"),ee=Symbol.for(\"react.client.reference\"),O=p.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,x=Object.prototype.hasOwnProperty,re=Array.isArray,A=console.createTask?console.createTask:function(){return null};p={\"react-stack-bottom-frame\":function(e){return e()}};var k,S={},te=p[\"react-stack-bottom-frame\"].bind(p,v)(),ne=A(b(v)),j={};h.Fragment=E,h.jsxDEV=function(e,r,a,i,u,f){var d=1e4>O.recentlyCreatedOwnerStacks++;return V(e,r,a,i,u,f,d?Error(\"react-stack-top-frame\"):te,d?A(b(e)):ne)}})()});var U=R((Re,I)=>{\"use strict\";I.exports=Y()});var _e={};ue(_e,{default:()=>pe,frontmatter:()=>me});var c=fe(U()),me={title:\"People\"};function F(t){let n=Object.assign({p:\"p\"},t.components);return(0,c.jsxDEV)(c.Fragment,{children:[(0,c.jsxDEV)(n.p,{children:\"EspirAI is a collective of expert AI and software engineers, designers, and product builders.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-48f6f3ab-7564-4d7e-9258-10a73901a321.mdx\",lineNumber:5,columnNumber:1},this),`\n`,(0,c.jsxDEV)(n.p,{children:\"We're distributed across time zones but united by a shared vision: building the infrastructure that enables the next generation of intelligent applications.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-48f6f3ab-7564-4d7e-9258-10a73901a321.mdx\",lineNumber:7,columnNumber:1},this),`\n`,(0,c.jsxDEV)(n.p,{children:\"Our team combines deep technical expertise with a pragmatic approach to solving real-world problems.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-48f6f3ab-7564-4d7e-9258-10a73901a321.mdx\",lineNumber:9,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-48f6f3ab-7564-4d7e-9258-10a73901a321.mdx\",lineNumber:1,columnNumber:1},this)}function be(t={}){let{wrapper:n}=t.components||{};return n?(0,c.jsxDEV)(n,Object.assign({},t,{children:(0,c.jsxDEV)(F,t,void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-48f6f3ab-7564-4d7e-9258-10a73901a321.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-48f6f3ab-7564-4d7e-9258-10a73901a321.mdx\"},this):F(t)}var pe=be;return de(_e);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "pages/who.mdx", "_raw": {"sourceFilePath": "pages/who.mdx", "sourceFileName": "who.mdx", "sourceFileDir": "pages", "contentType": "mdx", "flattenedPath": "pages/who"}, "type": "Page", "slug": "pages/who"}]