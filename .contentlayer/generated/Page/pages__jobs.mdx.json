{"title": "Jobs", "body": {"raw": "\nWe're looking for user-centric, craft-focused, pioneering minds who don't take themselves too seriously to join our team.\n\nWe're ambitious yet pragmatic. We run fast but sweat the details. We think the best way to invent the future is by relentlessly making progress every day.\n\n<Accordion title=\"Open Positions\">\n\n**Software Engineer, AI Platform**\n- Build the core infrastructure for AI agent orchestration\n- Design scalable systems for multi-agent workflows\n- Work with cutting-edge AI/ML technologies\n\n**Frontend Engineer, Developer Experience**\n- Create intuitive interfaces for AI agent development\n- Build developer tools and SDKs\n- Focus on user experience and developer productivity\n\n**AI Research Engineer**\n- Research and implement novel AI agent architectures\n- Optimize model performance and efficiency\n- Collaborate on breakthrough AI capabilities\n\n</Accordion>\n\n<Accordion title=\"What We Offer\">\n\n- **Competitive Compensation**: Equity + salary packages\n- **Cutting-Edge Work**: Build the future of AI agents\n- **Small Team Impact**: Your work directly shapes the product\n- **Learning Opportunities**: Work with world-class engineers\n- **Flexible Environment**: Remote-first with SF office access\n\n</Accordion>\n\nReady to join us? Type 'connect' to get in touch.", "code": "var Component=(()=>{var oe=Object.create;var _=Object.defineProperty;var de=Object.getOwnPropertyDescriptor;var se=Object.getOwnPropertyNames;var ae=Object.getPrototypeOf,be=Object.prototype.hasOwnProperty;var g=(t,n)=>()=>(n||t((n={exports:{}}).exports,n),n.exports),le=(t,n)=>{for(var d in n)_(t,d,{get:n[d],enumerable:!0})},j=(t,n,d,m)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let b of se(n))!be.call(t,b)&&b!==d&&_(t,b,{get:()=>n[b],enumerable:!(m=de(n,b))||m.enumerable});return t};var ce=(t,n,d)=>(d=t!=null?oe(ae(t)):{},j(n||!t||!t.__esModule?_(d,\"default\",{value:t,enumerable:!0}):d,t)),ue=t=>j(_({},\"__esModule\",{value:!0}),t);var C=g((he,P)=>{P.exports=React});var I=g(x=>{\"use strict\";(function(){function t(e){if(e==null)return null;if(typeof e==\"function\")return e.$$typeof===ee?null:e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case N:return\"Fragment\";case q:return\"Profiler\";case X:return\"StrictMode\";case H:return\"Suspense\";case Z:return\"SuspenseList\";case Q:return\"Activity\"}if(typeof e==\"object\")switch(typeof e.tag==\"number\"&&console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),e.$$typeof){case z:return\"Portal\";case G:return(e.displayName||\"Context\")+\".Provider\";case B:return(e._context.displayName||\"Context\")+\".Consumer\";case K:var i=e.render;return e=e.displayName,e||(e=i.displayName||i.name||\"\",e=e!==\"\"?\"ForwardRef(\"+e+\")\":\"ForwardRef\"),e;case J:return i=e.displayName||null,i!==null?i:t(e.type)||\"Memo\";case T:i=e._payload,e=e._init;try{return t(e(i))}catch{}}return null}function n(e){return\"\"+e}function d(e){try{n(e);var i=!1}catch{i=!0}if(i){i=console;var o=i.error,a=typeof Symbol==\"function\"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return o.call(i,\"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",a),n(e)}}function m(e){if(e===N)return\"<>\";if(typeof e==\"object\"&&e!==null&&e.$$typeof===T)return\"<...>\";try{var i=t(e);return i?\"<\"+i+\">\":\"<...>\"}catch{return\"<...>\"}}function b(){var e=R.A;return e===null?null:e.getOwner()}function w(){return Error(\"react-stack-top-frame\")}function F(e){if(k.call(e,\"key\")){var i=Object.getOwnPropertyDescriptor(e,\"key\").get;if(i&&i.isReactWarning)return!1}return e.key!==void 0}function M(e,i){function o(){A||(A=!0,console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",i))}o.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:o,configurable:!0})}function L(){var e=t(this.type);return O[e]||(O[e]=!0,console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\")),e=this.props.ref,e!==void 0?e:null}function $(e,i,o,a,l,c,u,y){return o=c.ref,e={$$typeof:v,type:e,key:i,props:c,_owner:l},(o!==void 0?o:null)!==null?Object.defineProperty(e,\"ref\",{enumerable:!1,get:L}):Object.defineProperty(e,\"ref\",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,\"_debugInfo\",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,\"_debugStack\",{configurable:!1,enumerable:!1,writable:!0,value:u}),Object.defineProperty(e,\"_debugTask\",{configurable:!1,enumerable:!1,writable:!0,value:y}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function V(e,i,o,a,l,c,u,y){var s=i.children;if(s!==void 0)if(a)if(ne(s)){for(a=0;a<s.length;a++)E(s[a]);Object.freeze&&Object.freeze(s)}else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else E(s);if(k.call(i,\"key\")){s=t(e);var f=Object.keys(i).filter(function(te){return te!==\"key\"});a=0<f.length?\"{key: someKey, \"+f.join(\": ..., \")+\": ...}\":\"{key: someKey}\",S[s+a]||(f=0<f.length?\"{\"+f.join(\": ..., \")+\": ...}\":\"{}\",console.error(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,a,s,f,s),S[s+a]=!0)}if(s=null,o!==void 0&&(d(o),s=\"\"+o),F(i)&&(d(i.key),s=\"\"+i.key),\"key\"in i){o={};for(var h in i)h!==\"key\"&&(o[h]=i[h])}else o=i;return s&&M(o,typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e),$(e,s,c,l,b(),o,u,y)}function E(e){typeof e==\"object\"&&e!==null&&e.$$typeof===v&&e._store&&(e._store.validated=1)}var p=C(),v=Symbol.for(\"react.transitional.element\"),z=Symbol.for(\"react.portal\"),N=Symbol.for(\"react.fragment\"),X=Symbol.for(\"react.strict_mode\"),q=Symbol.for(\"react.profiler\");Symbol.for(\"react.provider\");var B=Symbol.for(\"react.consumer\"),G=Symbol.for(\"react.context\"),K=Symbol.for(\"react.forward_ref\"),H=Symbol.for(\"react.suspense\"),Z=Symbol.for(\"react.suspense_list\"),J=Symbol.for(\"react.memo\"),T=Symbol.for(\"react.lazy\"),Q=Symbol.for(\"react.activity\"),ee=Symbol.for(\"react.client.reference\"),R=p.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,k=Object.prototype.hasOwnProperty,ne=Array.isArray,U=console.createTask?console.createTask:function(){return null};p={\"react-stack-bottom-frame\":function(e){return e()}};var A,O={},re=p[\"react-stack-bottom-frame\"].bind(p,w)(),ie=U(m(w)),S={};x.Fragment=N,x.jsxDEV=function(e,i,o,a,l,c){var u=1e4>R.recentlyCreatedOwnerStacks++;return V(e,i,o,a,l,c,u?Error(\"react-stack-top-frame\"):re,u?U(m(e)):ie)}})()});var W=g((xe,D)=>{\"use strict\";D.exports=I()});var Ne={};le(Ne,{default:()=>pe,frontmatter:()=>fe});var r=ce(W()),fe={title:\"Jobs\"};function Y(t){let n=Object.assign({p:\"p\",strong:\"strong\",ul:\"ul\",li:\"li\"},t.components),{Accordion:d}=n;return d||_e(\"Accordion\",!0,\"9:1-26:13\"),(0,r.jsxDEV)(r.Fragment,{children:[(0,r.jsxDEV)(n.p,{children:\"We're looking for user-centric, craft-focused, pioneering minds who don't take themselves too seriously to join our team.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:5,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.p,{children:\"We're ambitious yet pragmatic. We run fast but sweat the details. We think the best way to invent the future is by relentlessly making progress every day.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:7,columnNumber:1},this),`\n`,(0,r.jsxDEV)(d,{title:\"Open Positions\",children:[(0,r.jsxDEV)(n.p,{children:(0,r.jsxDEV)(n.strong,{children:\"Software Engineer, AI Platform\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:11,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:11,columnNumber:1},this),(0,r.jsxDEV)(n.ul,{children:[`\n`,(0,r.jsxDEV)(n.li,{children:\"Build the core infrastructure for AI agent orchestration\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:12,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.li,{children:\"Design scalable systems for multi-agent workflows\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.li,{children:\"Work with cutting-edge AI/ML technologies\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:14,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:12,columnNumber:1},this),(0,r.jsxDEV)(n.p,{children:(0,r.jsxDEV)(n.strong,{children:\"Frontend Engineer, Developer Experience\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:16,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:16,columnNumber:1},this),(0,r.jsxDEV)(n.ul,{children:[`\n`,(0,r.jsxDEV)(n.li,{children:\"Create intuitive interfaces for AI agent development\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:17,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.li,{children:\"Build developer tools and SDKs\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:18,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.li,{children:\"Focus on user experience and developer productivity\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:19,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:17,columnNumber:1},this),(0,r.jsxDEV)(n.p,{children:(0,r.jsxDEV)(n.strong,{children:\"AI Research Engineer\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:21,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:21,columnNumber:1},this),(0,r.jsxDEV)(n.ul,{children:[`\n`,(0,r.jsxDEV)(n.li,{children:\"Research and implement novel AI agent architectures\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:22,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.li,{children:\"Optimize model performance and efficiency\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.li,{children:\"Collaborate on breakthrough AI capabilities\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:24,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:22,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:9,columnNumber:1},this),`\n`,(0,r.jsxDEV)(d,{title:\"What We Offer\",children:(0,r.jsxDEV)(n.ul,{children:[`\n`,(0,r.jsxDEV)(n.li,{children:[(0,r.jsxDEV)(n.strong,{children:\"Competitive Compensation\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:30,columnNumber:3},this),\": Equity + salary packages\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:30,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.li,{children:[(0,r.jsxDEV)(n.strong,{children:\"Cutting-Edge Work\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:31,columnNumber:3},this),\": Build the future of AI agents\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:31,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.li,{children:[(0,r.jsxDEV)(n.strong,{children:\"Small Team Impact\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:32,columnNumber:3},this),\": Your work directly shapes the product\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:32,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.li,{children:[(0,r.jsxDEV)(n.strong,{children:\"Learning Opportunities\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:33,columnNumber:3},this),\": Work with world-class engineers\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:33,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.li,{children:[(0,r.jsxDEV)(n.strong,{children:\"Flexible Environment\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:34,columnNumber:3},this),\": Remote-first with SF office access\"]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:34,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:30,columnNumber:1},this)},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:28,columnNumber:1},this),`\n`,(0,r.jsxDEV)(n.p,{children:\"Ready to join us? Type 'connect' to get in touch.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:38,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\",lineNumber:1,columnNumber:1},this)}function me(t={}){let{wrapper:n}=t.components||{};return n?(0,r.jsxDEV)(n,Object.assign({},t,{children:(0,r.jsxDEV)(Y,t,void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx\"},this):Y(t)}var pe=me;function _e(t,n,d){throw new Error(\"Expected \"+(n?\"component\":\"object\")+\" `\"+t+\"` to be defined: you likely forgot to import, pass, or provide it.\"+(d?\"\\nIt\\u2019s referenced in your code at `\"+d+\"` in `/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-3380d93b-0023-4b0e-9c51-da3bbfbf1cf8.mdx`\":\"\"))}return ue(Ne);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "pages/jobs.mdx", "_raw": {"sourceFilePath": "pages/jobs.mdx", "sourceFileName": "jobs.mdx", "sourceFileDir": "pages", "contentType": "mdx", "flattenedPath": "pages/jobs"}, "type": "Page", "slug": "pages/jobs"}