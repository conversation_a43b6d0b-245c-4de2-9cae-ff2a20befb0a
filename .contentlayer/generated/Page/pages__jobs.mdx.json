{"title": "Jobs", "body": {"raw": "\nWe're looking for user-centric, craft-focused, pioneering minds who don't take themselves too seriously to join our team.\n\nWe're ambitious yet pragmatic. We run fast but sweat the details. We think the best way to invent the future is by relentlessly making progress every day.\n\nReady to join us? Type 'connect' to get in touch.", "code": "var Component=(()=>{var ae=Object.create;var p=Object.defineProperty;var ie=Object.getOwnPropertyDescriptor;var se=Object.getOwnPropertyNames;var ce=Object.getPrototypeOf,le=Object.prototype.hasOwnProperty;var T=(t,n)=>()=>(n||t((n={exports:{}}).exports,n),n.exports),ue=(t,n)=>{for(var s in n)p(t,s,{get:n[s],enumerable:!0})},P=(t,n,s,m)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let l of se(n))!le.call(t,l)&&l!==s&&p(t,l,{get:()=>n[l],enumerable:!(m=ie(n,l))||m.enumerable});return t};var fe=(t,n,s)=>(s=t!=null?ae(ce(t)):{},P(n||!t||!t.__esModule?p(s,\"default\",{value:t,enumerable:!0}):s,t)),de=t=>P(p({},\"__esModule\",{value:!0}),t);var D=T((ge,C)=>{C.exports=React});var Y=T(R=>{\"use strict\";(function(){function t(e){if(e==null)return null;if(typeof e==\"function\")return e.$$typeof===ee?null:e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case E:return\"Fragment\";case G:return\"Profiler\";case X:return\"StrictMode\";case H:return\"Suspense\";case Z:return\"SuspenseList\";case Q:return\"Activity\"}if(typeof e==\"object\")switch(typeof e.tag==\"number\"&&console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),e.$$typeof){case z:return\"Portal\";case K:return(e.displayName||\"Context\")+\".Provider\";case q:return(e._context.displayName||\"Context\")+\".Consumer\";case B:var r=e.render;return e=e.displayName,e||(e=r.displayName||r.name||\"\",e=e!==\"\"?\"ForwardRef(\"+e+\")\":\"ForwardRef\"),e;case J:return r=e.displayName||null,r!==null?r:t(e.type)||\"Memo\";case N:r=e._payload,e=e._init;try{return t(e(r))}catch{}}return null}function n(e){return\"\"+e}function s(e){try{n(e);var r=!1}catch{r=!0}if(r){r=console;var o=r.error,i=typeof Symbol==\"function\"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return o.call(r,\"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",i),n(e)}}function m(e){if(e===E)return\"<>\";if(typeof e==\"object\"&&e!==null&&e.$$typeof===N)return\"<...>\";try{var r=t(e);return r?\"<\"+r+\">\":\"<...>\"}catch{return\"<...>\"}}function l(){var e=O.A;return e===null?null:e.getOwner()}function h(){return Error(\"react-stack-top-frame\")}function F(e){if(k.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function M(e,r){function o(){S||(S=!0,console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",r))}o.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:o,configurable:!0})}function L(){var e=t(this.type);return A[e]||(A[e]=!0,console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\")),e=this.props.ref,e!==void 0?e:null}function $(e,r,o,i,u,f,d,g){return o=f.ref,e={$$typeof:w,type:e,key:r,props:f,_owner:u},(o!==void 0?o:null)!==null?Object.defineProperty(e,\"ref\",{enumerable:!1,get:L}):Object.defineProperty(e,\"ref\",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,\"_debugInfo\",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,\"_debugStack\",{configurable:!1,enumerable:!1,writable:!0,value:d}),Object.defineProperty(e,\"_debugTask\",{configurable:!1,enumerable:!1,writable:!0,value:g}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function V(e,r,o,i,u,f,d,g){var a=r.children;if(a!==void 0)if(i)if(re(a)){for(i=0;i<a.length;i++)v(a[i]);Object.freeze&&Object.freeze(a)}else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else v(a);if(k.call(r,\"key\")){a=t(e);var b=Object.keys(r).filter(function(oe){return oe!==\"key\"});i=0<b.length?\"{key: someKey, \"+b.join(\": ..., \")+\": ...}\":\"{key: someKey}\",x[a+i]||(b=0<b.length?\"{\"+b.join(\": ..., \")+\": ...}\":\"{}\",console.error(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,i,a,b,a),x[a+i]=!0)}if(a=null,o!==void 0&&(s(o),a=\"\"+o),F(r)&&(s(r.key),a=\"\"+r.key),\"key\"in r){o={};for(var y in r)y!==\"key\"&&(o[y]=r[y])}else o=r;return a&&M(o,typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e),$(e,a,f,u,l(),o,d,g)}function v(e){typeof e==\"object\"&&e!==null&&e.$$typeof===w&&e._store&&(e._store.validated=1)}var _=D(),w=Symbol.for(\"react.transitional.element\"),z=Symbol.for(\"react.portal\"),E=Symbol.for(\"react.fragment\"),X=Symbol.for(\"react.strict_mode\"),G=Symbol.for(\"react.profiler\");Symbol.for(\"react.provider\");var q=Symbol.for(\"react.consumer\"),K=Symbol.for(\"react.context\"),B=Symbol.for(\"react.forward_ref\"),H=Symbol.for(\"react.suspense\"),Z=Symbol.for(\"react.suspense_list\"),J=Symbol.for(\"react.memo\"),N=Symbol.for(\"react.lazy\"),Q=Symbol.for(\"react.activity\"),ee=Symbol.for(\"react.client.reference\"),O=_.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,k=Object.prototype.hasOwnProperty,re=Array.isArray,j=console.createTask?console.createTask:function(){return null};_={\"react-stack-bottom-frame\":function(e){return e()}};var S,A={},te=_[\"react-stack-bottom-frame\"].bind(_,h)(),ne=j(m(h)),x={};R.Fragment=E,R.jsxDEV=function(e,r,o,i,u,f){var d=1e4>O.recentlyCreatedOwnerStacks++;return V(e,r,o,i,u,f,d?Error(\"react-stack-top-frame\"):te,d?j(m(e)):ne)}})()});var I=T((Te,U)=>{\"use strict\";U.exports=Y()});var pe={};ue(pe,{default:()=>_e,frontmatter:()=>be});var c=fe(I()),be={title:\"Jobs\"};function W(t){let n=Object.assign({p:\"p\"},t.components);return(0,c.jsxDEV)(c.Fragment,{children:[(0,c.jsxDEV)(n.p,{children:\"We're looking for user-centric, craft-focused, pioneering minds who don't take themselves too seriously to join our team.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-d5046a54-b378-4ee8-936a-9bf964f64abc.mdx\",lineNumber:5,columnNumber:1},this),`\n`,(0,c.jsxDEV)(n.p,{children:\"We're ambitious yet pragmatic. We run fast but sweat the details. We think the best way to invent the future is by relentlessly making progress every day.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-d5046a54-b378-4ee8-936a-9bf964f64abc.mdx\",lineNumber:7,columnNumber:1},this),`\n`,(0,c.jsxDEV)(n.p,{children:\"Ready to join us? Type 'connect' to get in touch.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-d5046a54-b378-4ee8-936a-9bf964f64abc.mdx\",lineNumber:9,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-d5046a54-b378-4ee8-936a-9bf964f64abc.mdx\",lineNumber:1,columnNumber:1},this)}function me(t={}){let{wrapper:n}=t.components||{};return n?(0,c.jsxDEV)(n,Object.assign({},t,{children:(0,c.jsxDEV)(W,t,void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-d5046a54-b378-4ee8-936a-9bf964f64abc.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-d5046a54-b378-4ee8-936a-9bf964f64abc.mdx\"},this):W(t)}var _e=me;return de(pe);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "pages/jobs.mdx", "_raw": {"sourceFilePath": "pages/jobs.mdx", "sourceFileName": "jobs.mdx", "sourceFileDir": "pages", "contentType": "mdx", "flattenedPath": "pages/jobs"}, "type": "Page", "slug": "pages/jobs"}