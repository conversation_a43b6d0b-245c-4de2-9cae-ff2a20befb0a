{"title": "People", "body": {"raw": "\nEspirAI is a collective of expert AI and software engineers, designers, and product builders.\n\nWe're distributed across time zones but united by a shared vision: building the infrastructure that enables the next generation of intelligent applications.\n\nOur team combines deep technical expertise with a pragmatic approach to solving real-world problems.", "code": "var Component=(()=>{var oe=Object.create;var _=Object.defineProperty;var ie=Object.getOwnPropertyDescriptor;var se=Object.getOwnPropertyNames;var ce=Object.getPrototypeOf,le=Object.prototype.hasOwnProperty;var R=(t,n)=>()=>(n||t((n={exports:{}}).exports,n),n.exports),ue=(t,n)=>{for(var s in n)_(t,s,{get:n[s],enumerable:!0})},P=(t,n,s,b)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let l of se(n))!le.call(t,l)&&l!==s&&_(t,l,{get:()=>n[l],enumerable:!(b=ie(n,l))||b.enumerable});return t};var fe=(t,n,s)=>(s=t!=null?oe(ce(t)):{},P(n||!t||!t.__esModule?_(s,\"default\",{value:t,enumerable:!0}):s,t)),de=t=>P(_({},\"__esModule\",{value:!0}),t);var D=R((ge,C)=>{C.exports=React});var Y=R(h=>{\"use strict\";(function(){function t(e){if(e==null)return null;if(typeof e==\"function\")return e.$$typeof===ee?null:e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case E:return\"Fragment\";case G:return\"Profiler\";case X:return\"StrictMode\";case H:return\"Suspense\";case Z:return\"SuspenseList\";case J:return\"Activity\"}if(typeof e==\"object\")switch(typeof e.tag==\"number\"&&console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),e.$$typeof){case z:return\"Portal\";case K:return(e.displayName||\"Context\")+\".Provider\";case q:return(e._context.displayName||\"Context\")+\".Consumer\";case B:var r=e.render;return e=e.displayName,e||(e=r.displayName||r.name||\"\",e=e!==\"\"?\"ForwardRef(\"+e+\")\":\"ForwardRef\"),e;case Q:return r=e.displayName||null,r!==null?r:t(e.type)||\"Memo\";case N:r=e._payload,e=e._init;try{return t(e(r))}catch{}}return null}function n(e){return\"\"+e}function s(e){try{n(e);var r=!1}catch{r=!0}if(r){r=console;var a=r.error,i=typeof Symbol==\"function\"&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return a.call(r,\"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",i),n(e)}}function b(e){if(e===E)return\"<>\";if(typeof e==\"object\"&&e!==null&&e.$$typeof===N)return\"<...>\";try{var r=t(e);return r?\"<\"+r+\">\":\"<...>\"}catch{return\"<...>\"}}function l(){var e=O.A;return e===null?null:e.getOwner()}function v(){return Error(\"react-stack-top-frame\")}function M(e){if(x.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function W(e,r){function a(){k||(k=!0,console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",r))}a.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:a,configurable:!0})}function L(){var e=t(this.type);return S[e]||(S[e]=!0,console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\")),e=this.props.ref,e!==void 0?e:null}function $(e,r,a,i,u,f,d,g){return a=f.ref,e={$$typeof:w,type:e,key:r,props:f,_owner:u},(a!==void 0?a:null)!==null?Object.defineProperty(e,\"ref\",{enumerable:!1,get:L}):Object.defineProperty(e,\"ref\",{enumerable:!1,value:null}),e._store={},Object.defineProperty(e._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(e,\"_debugInfo\",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(e,\"_debugStack\",{configurable:!1,enumerable:!1,writable:!0,value:d}),Object.defineProperty(e,\"_debugTask\",{configurable:!1,enumerable:!1,writable:!0,value:g}),Object.freeze&&(Object.freeze(e.props),Object.freeze(e)),e}function V(e,r,a,i,u,f,d,g){var o=r.children;if(o!==void 0)if(i)if(re(o)){for(i=0;i<o.length;i++)y(o[i]);Object.freeze&&Object.freeze(o)}else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else y(o);if(x.call(r,\"key\")){o=t(e);var m=Object.keys(r).filter(function(ae){return ae!==\"key\"});i=0<m.length?\"{key: someKey, \"+m.join(\": ..., \")+\": ...}\":\"{key: someKey}\",j[o+i]||(m=0<m.length?\"{\"+m.join(\": ..., \")+\": ...}\":\"{}\",console.error(`A props object containing a \"key\" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />`,i,o,m,o),j[o+i]=!0)}if(o=null,a!==void 0&&(s(a),o=\"\"+a),M(r)&&(s(r.key),o=\"\"+r.key),\"key\"in r){a={};for(var T in r)T!==\"key\"&&(a[T]=r[T])}else a=r;return o&&W(a,typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e),$(e,o,f,u,l(),a,d,g)}function y(e){typeof e==\"object\"&&e!==null&&e.$$typeof===w&&e._store&&(e._store.validated=1)}var p=D(),w=Symbol.for(\"react.transitional.element\"),z=Symbol.for(\"react.portal\"),E=Symbol.for(\"react.fragment\"),X=Symbol.for(\"react.strict_mode\"),G=Symbol.for(\"react.profiler\");Symbol.for(\"react.provider\");var q=Symbol.for(\"react.consumer\"),K=Symbol.for(\"react.context\"),B=Symbol.for(\"react.forward_ref\"),H=Symbol.for(\"react.suspense\"),Z=Symbol.for(\"react.suspense_list\"),Q=Symbol.for(\"react.memo\"),N=Symbol.for(\"react.lazy\"),J=Symbol.for(\"react.activity\"),ee=Symbol.for(\"react.client.reference\"),O=p.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,x=Object.prototype.hasOwnProperty,re=Array.isArray,A=console.createTask?console.createTask:function(){return null};p={\"react-stack-bottom-frame\":function(e){return e()}};var k,S={},te=p[\"react-stack-bottom-frame\"].bind(p,v)(),ne=A(b(v)),j={};h.Fragment=E,h.jsxDEV=function(e,r,a,i,u,f){var d=1e4>O.recentlyCreatedOwnerStacks++;return V(e,r,a,i,u,f,d?Error(\"react-stack-top-frame\"):te,d?A(b(e)):ne)}})()});var U=R((Re,I)=>{\"use strict\";I.exports=Y()});var _e={};ue(_e,{default:()=>pe,frontmatter:()=>me});var c=fe(U()),me={title:\"People\"};function F(t){let n=Object.assign({p:\"p\"},t.components);return(0,c.jsxDEV)(c.Fragment,{children:[(0,c.jsxDEV)(n.p,{children:\"EspirAI is a collective of expert AI and software engineers, designers, and product builders.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-48f6f3ab-7564-4d7e-9258-10a73901a321.mdx\",lineNumber:5,columnNumber:1},this),`\n`,(0,c.jsxDEV)(n.p,{children:\"We're distributed across time zones but united by a shared vision: building the infrastructure that enables the next generation of intelligent applications.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-48f6f3ab-7564-4d7e-9258-10a73901a321.mdx\",lineNumber:7,columnNumber:1},this),`\n`,(0,c.jsxDEV)(n.p,{children:\"Our team combines deep technical expertise with a pragmatic approach to solving real-world problems.\"},void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-48f6f3ab-7564-4d7e-9258-10a73901a321.mdx\",lineNumber:9,columnNumber:1},this)]},void 0,!0,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-48f6f3ab-7564-4d7e-9258-10a73901a321.mdx\",lineNumber:1,columnNumber:1},this)}function be(t={}){let{wrapper:n}=t.components||{};return n?(0,c.jsxDEV)(n,Object.assign({},t,{children:(0,c.jsxDEV)(F,t,void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-48f6f3ab-7564-4d7e-9258-10a73901a321.mdx\"},this)}),void 0,!1,{fileName:\"/Users/<USER>/espirai-website/content/pages/_mdx_bundler_entry_point-48f6f3ab-7564-4d7e-9258-10a73901a321.mdx\"},this):F(t)}var pe=be;return de(_e);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "pages/who.mdx", "_raw": {"sourceFilePath": "pages/who.mdx", "sourceFileName": "who.mdx", "sourceFileDir": "pages", "contentType": "mdx", "flattenedPath": "pages/who"}, "type": "Page", "slug": "pages/who"}