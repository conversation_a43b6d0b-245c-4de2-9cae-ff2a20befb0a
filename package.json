{"name": "espirai-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest --watch"}, "dependencies": {"contentlayer": "^0.3.4", "next": "15.3.3", "next-contentlayer": "^0.3.4", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.8", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "typescript": "^5"}}