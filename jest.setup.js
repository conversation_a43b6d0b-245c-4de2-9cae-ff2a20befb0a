// ABOUTME: This file is run before each test file.
// ABOUTME: It imports jest-dom to provide custom matchers for asserting on DOM nodes.

import '@testing-library/jest-dom'

// Mock DOM methods that aren't available in jsdom
Object.defineProperty(HTMLElement.prototype, 'scrollIntoView', {
  value: jest.fn(),
  writable: true,
});

// Mock focus method
Object.defineProperty(HTMLElement.prototype, 'focus', {
  value: jest.fn(),
  writable: true,
});