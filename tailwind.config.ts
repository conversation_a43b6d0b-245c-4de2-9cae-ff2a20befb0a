// tailwind.config.ts

import type { Config } from 'tailwindcss';

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        // Add this part
        mono: ['var(--font-monaspace)', 'monospace'],
      },
      // You can also extend colors here later
      colors: {
        'brand-orange': '#ff6e40', // Example
      },
    },
  },
  plugins: [],
};
export default config;