// tailwind.config.ts

import type { Config } from 'tailwindcss';

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        mono: ['var(--font-monaspace)', 'monospace'],
      },
      colors: {
        'brand-orange': '#ff6e40',
        'terminal-bg': '#0a0a0a',
        'terminal-text': '#f0f0f0',
        'terminal-path': '#4a9eff',
        'terminal-prompt': '#8f8f8f',
        'terminal-command': '#ff6e40',
      },
      animation: {
        'blink': 'blink 1s step-end infinite',
      },
      keyframes: {
        blink: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0' },
        },
      },
      spacing: {
        'terminal-line': '1.7rem',
      },
    },
  },
  plugins: [],
};
export default config;
