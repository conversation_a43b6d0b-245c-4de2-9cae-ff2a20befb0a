# EspiraiOS - AI Agent Operating System

A sophisticated terminal-style website for EspirAI Labs, built as a guided qualification funnel that showcases our expertise in AI agent development.

## 🎯 Project Overview

EspiraiOS is more than a website—it's an interactive experience that embodies the identity of an elite AI/Software engineering firm. The terminal interface serves as a guided qualification funnel, filtering for technically-savvy decision-makers while demonstrating our capabilities.

### Core Philosophy
- **Guided Terminal**: Interactive browser-based OS that qualifies prospects
- **Clean & Efficient**: Minimalist, developer-native, professional aesthetic
- **Authentic**: Celebrates open-source tools and technical excellence

## 🚀 Features

### Terminal Interface
- **Interactive Commands**: `ls`, `cat`, `about`, `jobs`, `services`, `news`, `demo`
- **Fun Commands**: `neofetch`, `fortune`, `cowsay` for personality
- **Auto-completion**: Smart command suggestions and auto-population
- **Command History**: Navigate previous commands with arrow keys

### Content Management
- **MDX Integration**: Rich content with interactive components
- **Accordion Components**: Expandable content sections
- **Code Playground**: Interactive code editor with mock execution
- **Dynamic Routing**: SEO-friendly static pages for all content

### Advanced Features
- **Connect Flow**: Multi-step qualification questionnaire
- **Responsive Design**: Mobile-first approach with clean typography
- **Type Safety**: Full TypeScript coverage
- **Testing**: Comprehensive Jest + React Testing Library suite

## 🛠 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Runtime**: React 19
- **Language**: TypeScript
- **Styling**: Tailwind CSS 4.1.8
- **Content**: Contentlayer + MDX
- **Testing**: Jest + React Testing Library
- **Deployment**: Cloudflare Pages

## 🏃‍♂️ Getting Started

```bash
# Install dependencies
bun install

# Start development server
bun run dev

# Run tests
bun run test

# Build for production
bun run build
```

Open [http://localhost:3000](http://localhost:3000) to experience the terminal interface.

## 🧪 Testing

```bash
# Run all tests
bun run test

# Watch mode for development
bun run test:watch
```

## 🚀 Deployment

### Cloudflare Pages
```bash
# Deploy to production
bun run deploy

# Deploy preview
bun run deploy:preview
```

**EspirAI Labs** - Building the next-gen operating system for AI agents
