// ABOUTME: This is the Jest configuration file for our Next.js project.
// ABOUTME: It uses the `next/jest` preset to handle SWC, TypeScript, and other Next.js-specific setups automatically.

const nextJest = require('next/jest')

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files in your test environment
  dir: './',
})

// Add any custom config to be passed to Jest
const customJestConfig = {
  // Add more setup options before each test is run
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jest-environment-jsdom',
  // This is crucial for telling <PERSON><PERSON> to look in the root directory for modules,
  // which works with the baseUrl setting in tsconfig.json
  moduleDirectories: ['node_modules', '<rootDir>/'],
}

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig)