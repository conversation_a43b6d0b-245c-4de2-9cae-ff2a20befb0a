// ABOUTME: This is the Jest configuration file for our Next.js project.
// ABOUTME: It uses the `next/jest` preset to handle SWC, TypeScript, and other Next.js-specific setups automatically.

const nextJest = require('next/jest')

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files in your test environment
  dir: './',
})

// Add any custom config to be passed to Jest
const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jest-environment-jsdom',
  moduleDirectories: ['node_modules', '<rootDir>/'],

  // Module name mapping for path aliases and mocking problematic modules
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    // Mock next-contentlayer hooks to avoid ES module issues
    '^next-contentlayer/hooks$': '<rootDir>/src/__mocks__/next-contentlayer-hooks.js',
  },

  // This is the fix for the "Unexpected token 'export'" error.
  // It tells <PERSON><PERSON> to process `contentlayer` and `next-contentlayer` from node_modules.
  transformIgnorePatterns: [
    '/node_modules/(?!(contentlayer|next-contentlayer)/)',
  ],
}

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig)